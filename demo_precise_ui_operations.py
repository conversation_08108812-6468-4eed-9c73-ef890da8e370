#!/usr/bin/env python3
"""演示精准UI操作功能"""

import logging
from desk_pilot.core import DeskPilotCore
import time

# 设置详细日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')

def demo_precise_ui_operations():
    print('🎯 演示精准UI操作功能')
    print('=' * 50)
    
    # 创建DeskPilot实例（启用可视化模式以便观察）
    core = DeskPilotCore(visual_mode=True, operation_delay=1.0)
    
    # 1. 获取窗口信息
    print('\n📍 步骤1: 获取QuantumSeek窗口信息')
    window_info = core.get_window_info('QuantumSeek')
    
    if not window_info:
        print('❌ 未找到QuantumSeek窗口')
        return
    
    print(f'✅ 窗口信息:')
    print(f'   标题: {window_info["title"]}')
    print(f'   位置: ({window_info["left"]}, {window_info["top"]})')
    print(f'   尺寸: {window_info["width"]} x {window_info["height"]}')
    print(f'   中心点: ({window_info["center_x"]}, {window_info["center_y"]})')
    print(f'   副显示器: {window_info["is_on_secondary_monitor"]}')
    
    # 2. 演示绝对坐标点击
    print('\n🖱️ 步骤2: 演示绝对坐标点击')
    print('点击窗口中心（绝对坐标）...')
    
    result = core.perform_ui_action(
        action_type="click",
        window_title="QuantumSeek",
        x=window_info["center_x"],
        y=window_info["center_y"],
        relative_coords=False
    )
    
    if result["success"]:
        print(f'✅ 绝对坐标点击成功: {result["coordinates"]}')
    else:
        print(f'❌ 绝对坐标点击失败: {result["error"]}')
    
    time.sleep(2)
    
    # 3. 演示相对坐标点击
    print('\n🎯 步骤3: 演示相对坐标点击')
    print('点击窗口左上角区域（相对坐标）...')
    
    # 相对坐标：距离窗口左上角100像素右，50像素下
    relative_x, relative_y = 100, 50
    
    result = core.perform_ui_action(
        action_type="click",
        window_title="QuantumSeek",
        x=relative_x,
        y=relative_y,
        relative_coords=True
    )
    
    if result["success"]:
        print(f'✅ 相对坐标点击成功:')
        print(f'   原始相对坐标: {result["original_coordinates"]}')
        print(f'   转换后绝对坐标: {result["coordinates"]}')
    else:
        print(f'❌ 相对坐标点击失败: {result["error"]}')
    
    time.sleep(2)
    
    # 4. 演示相对坐标滚动
    print('\n📜 步骤4: 演示相对坐标滚动')
    print('在窗口中心区域滚动（相对坐标）...')
    
    # 相对坐标：窗口中心
    center_relative_x = window_info["width"] // 2
    center_relative_y = window_info["height"] // 2
    
    result = core.perform_ui_action(
        action_type="scroll",
        window_title="QuantumSeek",
        x=center_relative_x,
        y=center_relative_y,
        clicks=3,
        relative_coords=True
    )
    
    if result["success"]:
        print(f'✅ 相对坐标滚动成功:')
        print(f'   原始相对坐标: {result["original_coordinates"]}')
        print(f'   转换后绝对坐标: {result["coordinates"]}')
        print(f'   滚动步数: {result["clicks"]}')
    else:
        print(f'❌ 相对坐标滚动失败: {result["error"]}')
    
    time.sleep(2)
    
    # 5. 演示坐标验证
    print('\n🔍 步骤5: 演示坐标验证')
    
    # 测试窗口边界外的坐标
    print('测试窗口边界外的坐标...')
    
    result = core.perform_ui_action(
        action_type="click",
        window_title="QuantumSeek",
        x=window_info["width"] + 100,  # 超出窗口右边界
        y=50,
        relative_coords=True
    )
    
    if result["success"]:
        print(f'⚠️ 边界外坐标点击成功（可能需要改进验证）: {result["coordinates"]}')
    else:
        print(f'✅ 边界外坐标被正确拒绝: {result["error"]}')
    
    # 6. 演示多个精准操作序列
    print('\n🎭 步骤6: 演示精准操作序列')
    print('执行一系列精准操作...')
    
    operations = [
        {"desc": "点击左上角", "x": 50, "y": 30, "action": "click"},
        {"desc": "点击右上角", "x": window_info["width"] - 50, "y": 30, "action": "click"},
        {"desc": "点击左下角", "x": 50, "y": window_info["height"] - 50, "action": "click"},
        {"desc": "点击右下角", "x": window_info["width"] - 50, "y": window_info["height"] - 50, "action": "click"},
    ]
    
    for i, op in enumerate(operations, 1):
        print(f'   {i}. {op["desc"]} - 相对坐标({op["x"]}, {op["y"]})')
        
        result = core.perform_ui_action(
            action_type=op["action"],
            window_title="QuantumSeek",
            x=op["x"],
            y=op["y"],
            relative_coords=True
        )
        
        if result["success"]:
            abs_coords = result["coordinates"]
            print(f'      ✅ 成功 -> 绝对坐标({abs_coords[0]}, {abs_coords[1]})')
        else:
            print(f'      ❌ 失败: {result["error"]}')
        
        time.sleep(1)  # 操作间隔
    
    # 7. 总结
    print('\n📋 步骤7: 演示总结')
    print('=' * 50)
    print('✅ 精准UI操作功能演示完成！')
    print('')
    print('🎯 主要功能:')
    print('   ✅ 绝对坐标操作 - 直接使用屏幕坐标')
    print('   ✅ 相对坐标操作 - 基于窗口内坐标自动转换')
    print('   ✅ 多显示器支持 - 正确处理副显示器窗口')
    print('   ✅ 坐标验证 - 确保操作在窗口范围内')
    print('   ✅ 可视化模式 - 清晰显示操作过程')
    print('')
    print('💡 使用建议:')
    print('   • 使用相对坐标进行窗口内元素操作')
    print('   • 使用绝对坐标进行跨窗口操作')
    print('   • 启用可视化模式便于调试和演示')
    print('   • 设置适当的操作延迟避免操作过快')

if __name__ == '__main__':
    demo_precise_ui_operations()
