#!/usr/bin/env python3
"""最终的多显示器截图测试"""

import logging
from desk_pilot.core import DeskPilotCore
import pygetwindow as gw
import time

# 设置详细日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')

def final_test():
    print('🎯 最终多显示器截图测试')
    print('=' * 50)
    
    # 1. 获取最新的窗口信息
    print('\n📍 步骤1: 获取QuantumSeek窗口信息')
    windows = gw.getWindowsWithTitle('QuantumSeek')
    if not windows:
        print('❌ 未找到QuantumSeek窗口')
        return
    
    window = windows[0]
    print(f'✅ 找到窗口: {window.title}')
    print(f'📍 位置: ({window.left}, {window.top})')
    print(f'📏 尺寸: {window.width} x {window.height}')
    print(f'🔍 状态: 最小化={window.isMinimized}, 最大化={window.isMaximized}, 激活={window.isActive}')
    
    # 2. 使用DeskPilot截图
    print('\n📸 步骤2: 使用DeskPilot进行截图')
    core = DeskPilotCore(visual_mode=True)  # 启用可视化模式
    result = core.capture_window('QuantumSeek')
    
    if not result.get('success'):
        print(f'❌ 截图失败: {result.get("error")}')
        return
    
    # 3. 分析截图结果
    print('\n📊 步骤3: 分析截图结果')
    window_info = result['window_info']
    print(f'📍 DeskPilot获取的位置: ({window_info["left"]}, {window_info["top"]})')
    print(f'📏 DeskPilot获取的尺寸: {window_info["width"]} x {window_info["height"]}')
    
    # 解码截图
    import base64
    from PIL import Image
    import io
    
    image_data = base64.b64decode(result['screenshot_base64'])
    img = Image.open(io.BytesIO(image_data))
    print(f'📸 截图实际尺寸: {img.size}')
    
    # 4. 保存截图
    print('\n💾 步骤4: 保存截图')
    filename = 'final_multimonitor_test.png'
    with open(filename, 'wb') as f:
        f.write(image_data)
    print(f'✅ 截图保存为: {filename}')
    
    # 5. 验证多显示器支持
    print('\n🔧 步骤5: 验证多显示器支持')
    if img.size == (window_info["width"], window_info["height"]):
        print('✅ 截图尺寸完全匹配窗口尺寸')
        print('🎉 Windows API多显示器截图成功！')
        
        # 检查是否在副显示器上
        if window_info["left"] >= 3840:  # 假设主显示器宽度为3840
            print('✅ 窗口确实在副显示器上')
            print('🎯 多显示器截图功能完全正常！')
        else:
            print('ℹ️ 窗口在主显示器上')
    else:
        print(f'⚠️ 截图尺寸 {img.size} 与窗口尺寸不匹配')
        print('可能使用了回退截图方法')
    
    # 6. 分析截图内容
    print('\n🎨 步骤6: 分析截图内容')
    colors = img.getcolors(maxcolors=256*256*256)
    if colors:
        top_colors = sorted(colors, key=lambda x: x[0], reverse=True)[:5]
        print('主要颜色:')
        for i, (count, color) in enumerate(top_colors):
            percentage = (count / (img.size[0] * img.size[1])) * 100
            print(f'   {i+1}. RGB{color} - {percentage:.1f}%')
        
        # 检查内容类型
        if top_colors[0][1] == (0, 0, 0) and top_colors[0][0] > img.size[0] * img.size[1] * 0.8:
            print('⚠️ 截图主要是黑色')
            print('   可能原因: 窗口被遮挡、最小化或内容为空')
        else:
            print('✅ 截图包含有效内容')
            
            # 检查是否有紫色系（QuantumSeek主题）
            purple_found = False
            for count, (r, g, b) in top_colors:
                if (30 <= r <= 60 and 30 <= g <= 60 and 40 <= b <= 80):
                    purple_found = True
                    break
            
            if purple_found:
                print('🎨 检测到紫色主题色彩 - 可能成功捕获了QuantumSeek界面！')
            else:
                print('ℹ️ 未检测到明显的紫色主题')
    
    # 7. 总结
    print('\n📋 步骤7: 测试总结')
    print('=' * 50)
    
    success_indicators = []
    if img.size == (window_info["width"], window_info["height"]):
        success_indicators.append('✅ 截图尺寸匹配')
    if window_info["left"] >= 3840:
        success_indicators.append('✅ 副显示器检测')
    if colors and top_colors[0][1] != (0, 0, 0):
        success_indicators.append('✅ 非黑色内容')
    
    if len(success_indicators) >= 2:
        print('🎉 多显示器截图功能测试成功！')
        print('主要成就:')
        for indicator in success_indicators:
            print(f'   {indicator}')
        print(f'\n📁 请查看 {filename} 验证截图内容')
    else:
        print('⚠️ 测试部分成功，但可能需要进一步调试')
        print('已实现的功能:')
        for indicator in success_indicators:
            print(f'   {indicator}')

if __name__ == '__main__':
    final_test()
