#!/usr/bin/env python3
"""直接测试DeskPilot中的Windows API代码"""

import logging
from desk_pilot.core import DeskPilotCore
import pygetwindow as gw

# 设置详细日志
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s:%(name)s:%(message)s')

def test_deskpilot_win32():
    print('🔍 直接测试DeskPilot中的Windows API代码...')
    
    # 获取QuantumSeek窗口
    windows = gw.getWindowsWithTitle('QuantumSeek')
    if not windows:
        print('❌ 未找到QuantumSeek窗口')
        return
    
    window = windows[0]
    print(f'📍 窗口: {window.title}')
    print(f'📍 位置: ({window.left}, {window.top})')
    print(f'📏 尺寸: {window.width} x {window.height}')
    
    # 创建DeskPilot实例
    core = DeskPilotCore(visual_mode=False)
    
    # 直接调用capture_window
    print('\n🔧 调用DeskPilot capture_window...')
    result = core.capture_window('QuantumSeek')
    
    if result.get('success'):
        print('✅ 截图成功！')
        window_info = result['window_info']
        print(f'📍 返回的窗口位置: ({window_info["left"]}, {window_info["top"]})')
        print(f'📏 返回的窗口尺寸: {window_info["width"]} x {window_info["height"]}')
        
        # 分析截图
        import base64
        from PIL import Image
        import io
        
        image_data = base64.b64decode(result['screenshot_base64'])
        img = Image.open(io.BytesIO(image_data))
        print(f'📸 截图尺寸: {img.size}')
        
        # 保存截图
        with open('deskpilot_win32_test.png', 'wb') as f:
            f.write(image_data)
        print('💾 截图保存为: deskpilot_win32_test.png')
        
        # 检查是否成功使用了Windows API
        if img.size == (window_info["width"], window_info["height"]):
            print('🎉 成功！截图尺寸匹配窗口尺寸 - Windows API工作正常！')
            
            # 分析颜色
            colors = img.getcolors(maxcolors=256*256*256)
            if colors:
                top_colors = sorted(colors, key=lambda x: x[0], reverse=True)[:5]
                print('\n🎨 截图中的主要颜色:')
                for i, (count, color) in enumerate(top_colors):
                    percentage = (count / (img.size[0] * img.size[1])) * 100
                    print(f'   {i+1}. {color} - {percentage:.1f}%')
                    
                # 检查紫色系
                purple_found = False
                for count, (r, g, b) in top_colors:
                    if (30 <= r <= 50 and 30 <= g <= 50 and 40 <= b <= 60):
                        purple_found = True
                        break
                
                if purple_found:
                    print('✅ 检测到QuantumSeek的紫色主题！')
                else:
                    print('⚠️ 未检测到明显的紫色主题')
        else:
            print(f'⚠️ 截图尺寸 {img.size} 与窗口尺寸不匹配，可能使用了回退方法')
    else:
        print(f'❌ 截图失败: {result.get("error")}')

if __name__ == '__main__':
    test_deskpilot_win32()
