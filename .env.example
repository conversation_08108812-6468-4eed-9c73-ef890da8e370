# DeskPilot MCP 环境变量配置
# 复制此文件为 .env 并根据需要修改值

# === 基础配置 ===
NODE_ENV=development
DEBUG=deskpilot:*

# === MCP 配置 ===
MCP_TIMEOUT=30000
MCP_TOOL_TIMEOUT=10000

# === DeskPilot 配置 ===
DESKPILOT_LOG_LEVEL=info
DESKPILOT_SCREENSHOT_FORMAT=png
DESKPILOT_SCREENSHOT_QUALITY=90
DESKPILOT_WINDOW_TIMEOUT=5000

# === Tauri 配置 ===
TAURI_DEV_TIMEOUT=60000
TAURI_BUILD_TIMEOUT=300000

# === 调试配置 ===
# 可选值: error, warn, info, debug, trace
LOG_LEVEL=info

# === 性能配置 ===
# 截图缓存时间（毫秒）
SCREENSHOT_CACHE_TTL=5000

# 窗口操作重试次数
WINDOW_OPERATION_RETRIES=3

# === 安全配置 ===
# 是否允许执行系统命令
ALLOW_SYSTEM_COMMANDS=true

# 是否允许文件操作
ALLOW_FILE_OPERATIONS=true
