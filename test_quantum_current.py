#!/usr/bin/env python3
"""使用当前QuantumSeek窗口坐标测试截图"""

import win32gui
import win32ui
import win32con
import win32api
from PIL import Image
import pygetwindow as gw

def capture_quantum_current():
    """使用当前QuantumSeek窗口坐标截图"""
    print('🔍 使用当前QuantumSeek坐标测试截图...')
    
    try:
        # 获取QuantumSeek窗口
        windows = gw.getWindowsWithTitle('QuantumSeek')
        if not windows:
            print('❌ 未找到QuantumSeek窗口')
            return None
        
        window = windows[0]
        print(f'📍 QuantumSeek窗口: ({window.left}, {window.top}) {window.width}x{window.height}')
        
        # 激活窗口
        if not window.isActive:
            window.activate()
            import time
            time.sleep(0.5)
            print('✅ 窗口已激活')
        
        # 使用Windows API截取窗口区域
        hdesktop = win32gui.GetDesktopWindow()
        desktop_dc = win32gui.GetWindowDC(hdesktop)
        img_dc = win32ui.CreateDCFromHandle(desktop_dc)
        mem_dc = img_dc.CreateCompatibleDC()
        
        # 创建位图
        screenshot = win32ui.CreateBitmap()
        screenshot.CreateCompatibleBitmap(img_dc, window.width, window.height)
        mem_dc.SelectObject(screenshot)
        
        # 复制窗口区域到位图
        mem_dc.BitBlt((0, 0), (window.width, window.height), img_dc, (window.left, window.top), win32con.SRCCOPY)
        
        # 获取位图数据
        bmpinfo = screenshot.GetInfo()
        bmpstr = screenshot.GetBitmapBits(True)
        
        # 转换为PIL Image
        img = Image.frombuffer(
            'RGB',
            (bmpinfo['bmWidth'], bmpinfo['bmHeight']),
            bmpstr, 'raw', 'BGRX', 0, 1
        )
        
        # 清理资源
        mem_dc.DeleteDC()
        img_dc.DeleteDC()
        win32gui.ReleaseDC(hdesktop, desktop_dc)
        win32gui.DeleteObject(screenshot.GetHandle())
        
        print(f'✅ 成功捕获QuantumSeek窗口: {img.size}')
        
        # 保存截图
        img.save('quantum_current_win32.png')
        print('💾 截图已保存为: quantum_current_win32.png')
        
        # 分析颜色
        colors = img.getcolors(maxcolors=256*256*256)
        if colors:
            top_colors = sorted(colors, key=lambda x: x[0], reverse=True)[:10]
            print('\n🎨 截图中最常见的颜色:')
            for i, (count, color) in enumerate(top_colors):
                percentage = (count / (img.size[0] * img.size[1])) * 100
                print(f'   {i+1}. {color} - {percentage:.1f}%')
                
            # 检查紫色/粉色 - 更宽泛的检测
            has_purple = False
            purple_colors = []
            for count, (r, g, b) in top_colors:
                # 更宽泛的紫色检测
                if (r > 50 and b > 50 and (r + b) > g * 1.5) or \
                   (r > 80 and g > 30 and b > 80) or \
                   (r > 100 and g < r and b > 60):
                    has_purple = True
                    purple_colors.append((r, g, b))
            
            if has_purple:
                print(f'✅ 检测到可能的紫色/粉色主题: {purple_colors}')
                print('🎯 可能成功捕获QuantumSeek界面！')
            else:
                print('❌ 未检测到紫色主题')
                
                # 检查是否全黑
                if top_colors[0][1] == (0, 0, 0) and top_colors[0][0] > img.size[0] * img.size[1] * 0.8:
                    print('⚠️ 截图主要是黑色，可能窗口被遮挡或最小化')
        
        return img
        
    except Exception as e:
        print(f'❌ 截图失败: {str(e)}')
        import traceback
        traceback.print_exc()
        return None

if __name__ == '__main__':
    capture_quantum_current()
