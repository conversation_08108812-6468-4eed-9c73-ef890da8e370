#!/usr/bin/env python3
"""测试Windows截图工具是否能截取QuantumSeek"""

import subprocess
import time
import os
import pygetwindow as gw

def test_windows_snipping_tool():
    print('🔧 测试Windows截图工具')
    print('=' * 40)
    
    # 1. 获取QuantumSeek窗口信息
    print('\n📍 步骤1: 获取QuantumSeek窗口信息')
    windows = gw.getWindowsWithTitle('QuantumSeek')
    
    if not windows:
        print('❌ 未找到QuantumSeek窗口')
        return
    
    window = windows[0]
    print(f'✅ 窗口标题: {window.title}')
    print(f'📍 位置: ({window.left}, {window.top})')
    print(f'📏 尺寸: {window.width} x {window.height}')
    
    # 2. 激活QuantumSeek窗口
    print('\n🔄 步骤2: 激活QuantumSeek窗口')
    try:
        window.activate()
        time.sleep(2)
        print('✅ 窗口已激活')
    except Exception as e:
        print(f'⚠️ 激活失败: {e}')
    
    # 3. 启动Windows截图工具
    print('\n📸 步骤3: 启动Windows截图工具')
    print('正在启动Windows截图工具...')
    print('请手动截取QuantumSeek窗口并保存为 windows_snipping_quantum.png')
    print('完成后按任意键继续...')
    
    try:
        # 启动截图工具
        subprocess.Popen(['ms-screenclip:'])
        
        # 等待用户完成截图
        input('\n按回车键继续...')
        
    except Exception as e:
        print(f'⚠️ 启动截图工具失败: {e}')
        print('请手动使用 Win + Shift + S 截图')
        input('完成截图后按回车键继续...')
    
    # 4. 检查截图文件
    print('\n🔍 步骤4: 检查截图结果')
    
    possible_files = [
        'windows_snipping_quantum.png',
        'Screenshot.png',
        'QuantumSeek.png'
    ]
    
    found_file = None
    for filename in possible_files:
        if os.path.exists(filename):
            found_file = filename
            break
    
    if found_file:
        print(f'✅ 找到截图文件: {found_file}')
        
        # 分析截图
        try:
            from PIL import Image
            img = Image.open(found_file)
            print(f'📏 截图尺寸: {img.size}')
            
            # 检查颜色
            colors = img.getcolors(maxcolors=256*256*256)
            if colors:
                top_colors = sorted(colors, key=lambda x: x[0], reverse=True)[:5]
                total_pixels = img.size[0] * img.size[1]
                
                print('\n🎨 主要颜色:')
                for i, (count, color) in enumerate(top_colors):
                    percentage = (count / total_pixels) * 100
                    print(f'   {i+1}. RGB{color} - {percentage:5.1f}%')
                
                # 检查是否是黑屏
                if top_colors[0][1] == (0, 0, 0) and top_colors[0][0] > total_pixels * 0.8:
                    print('\n❌ Windows截图工具也显示黑色')
                    print('🔍 这确认了QuantumSeek有防截图保护')
                else:
                    print('\n✅ Windows截图工具成功获取内容')
                    print('🔍 这说明问题在于编程方式截图的限制')
                    
        except Exception as e:
            print(f'⚠️ 分析截图失败: {e}')
    else:
        print('❌ 未找到截图文件')
        print('请确保截图已保存到当前目录')
    
    # 5. 尝试其他截图工具
    print('\n🛠️ 步骤5: 尝试其他截图方法')
    
    # 尝试使用PowerShell截图
    print('\n📷 尝试PowerShell截图')
    try:
        ps_script = f'''
Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing

$screen = [System.Windows.Forms.Screen]::AllScreens | Where-Object {{$_.Primary -eq $false}} | Select-Object -First 1
if ($screen -eq $null) {{
    $screen = [System.Windows.Forms.Screen]::PrimaryScreen
}}

$bounds = $screen.Bounds
$bitmap = New-Object System.Drawing.Bitmap $bounds.Width, $bounds.Height
$graphics = [System.Drawing.Graphics]::FromImage($bitmap)
$graphics.CopyFromScreen($bounds.Location, [System.Drawing.Point]::Empty, $bounds.Size)
$bitmap.Save("powershell_screenshot.png", [System.Drawing.Imaging.ImageFormat]::Png)
$graphics.Dispose()
$bitmap.Dispose()
'''
        
        with open('screenshot.ps1', 'w') as f:
            f.write(ps_script)
        
        result = subprocess.run(['powershell', '-ExecutionPolicy', 'Bypass', '-File', 'screenshot.ps1'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0 and os.path.exists('powershell_screenshot.png'):
            print('✅ PowerShell截图成功')
            
            # 裁剪QuantumSeek区域
            from PIL import Image
            full_img = Image.open('powershell_screenshot.png')
            
            # 计算相对坐标
            left = max(0, window.left - 3840)  # 假设副屏从3840开始
            top = window.top
            right = left + window.width
            bottom = top + window.height
            
            if left >= 0 and top >= 0 and right <= full_img.width and bottom <= full_img.height:
                cropped = full_img.crop((left, top, right, bottom))
                cropped.save('powershell_quantum_cropped.png')
                
                # 检查内容
                colors = cropped.getcolors(maxcolors=256*256*256)
                if colors:
                    top_color = max(colors, key=lambda x: x[0])
                    if top_color[1] == (0, 0, 0) and top_color[0] > cropped.size[0] * cropped.size[1] * 0.8:
                        print('❌ PowerShell截图也是黑色')
                    else:
                        print('✅ PowerShell截图包含内容')
            else:
                print('⚠️ 坐标超出截图范围')
        else:
            print(f'❌ PowerShell截图失败: {result.stderr}')
            
    except Exception as e:
        print(f'❌ PowerShell截图异常: {e}')
    
    # 6. 总结
    print('\n📋 总结')
    print('=' * 40)
    print('测试结果:')
    print('1. Python PIL ImageGrab: ❌ 黑屏')
    print('2. Python Windows API: ❌ 黑屏')
    print('3. Python PrintWindow: ❌ 黑屏')
    print('4. Windows截图工具: 待确认')
    print('5. PowerShell截图: 待确认')
    print('')
    print('结论:')
    print('如果所有编程方式都显示黑屏，而手动截图工具正常，')
    print('则说明QuantumSeek实现了防编程截图保护机制。')
    print('')
    print('这是一些现代应用（特别是Electron应用）的')
    print('安全特性，用于防止恶意软件截取敏感内容。')

if __name__ == '__main__':
    test_windows_snipping_tool()
