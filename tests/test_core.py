"""
Tests for DeskPilot Core functionality
"""

import pytest
import sys
from unittest.mock import Mock, patch, MagicMock

# Only run tests on Windows
pytestmark = pytest.mark.skipif(sys.platform != "win32", reason="Windows only")

from desk_pilot.core import DeskPilotCore


class TestDeskPilotCore:
    """Test DeskPilot Core functionality"""
    
    def setup_method(self):
        """Setup test environment"""
        self.core = DeskPilotCore()
    
    def test_init(self):
        """Test DeskPilotCore initialization"""
        assert self.core is not None
        assert hasattr(self.core, 'logger')
    
    @patch('desk_pilot.core.pyautogui.screenshot')
    def test_capture_window_no_title(self, mock_screenshot):
        """Test capturing active window without title"""
        # Mock screenshot
        mock_image = Mock()
        mock_image.save = Mock()
        mock_screenshot.return_value = mock_image
        
        # Mock BytesIO and base64
        with patch('desk_pilot.core.BytesIO') as mock_bytesio, \
             patch('desk_pilot.core.base64.b64encode') as mock_b64encode:
            
            mock_buffer = Mock()
            mock_bytesio.return_value = mock_buffer
            mock_buffer.getvalue.return_value = b'fake_image_data'
            mock_b64encode.return_value = b'fake_base64_data'
            
            result = self.core.capture_window()
            
            assert result['success'] is True
            assert 'screenshot' in result
            assert result['window_title'] == 'Active Window'
            mock_screenshot.assert_called_once()
    
    @patch('desk_pilot.core.pygetwindow.getWindowsWithTitle')
    @patch('desk_pilot.core.pyautogui.screenshot')
    def test_capture_window_with_title(self, mock_screenshot, mock_get_windows):
        """Test capturing specific window by title"""
        # Mock window
        mock_window = Mock()
        mock_window.left = 100
        mock_window.top = 100
        mock_window.width = 800
        mock_window.height = 600
        mock_get_windows.return_value = [mock_window]
        
        # Mock screenshot
        mock_image = Mock()
        mock_image.save = Mock()
        mock_screenshot.return_value = mock_image
        
        with patch('desk_pilot.core.BytesIO') as mock_bytesio, \
             patch('desk_pilot.core.base64.b64encode') as mock_b64encode:
            
            mock_buffer = Mock()
            mock_bytesio.return_value = mock_buffer
            mock_buffer.getvalue.return_value = b'fake_image_data'
            mock_b64encode.return_value = b'fake_base64_data'
            
            result = self.core.capture_window("Test Window")
            
            assert result['success'] is True
            assert result['window_title'] == 'Test Window'
            mock_get_windows.assert_called_once_with("Test Window")
    
    @patch('desk_pilot.core.pyautogui.click')
    def test_perform_ui_action_click(self, mock_click):
        """Test UI click action"""
        result = self.core.perform_ui_action(
            action_type="click",
            x=100,
            y=200
        )
        
        assert result['success'] is True
        assert result['action'] == 'click'
        mock_click.assert_called_once_with(100, 200, button='left', clicks=1, interval=0.1)
    
    @patch('desk_pilot.core.pyautogui.typewrite')
    def test_perform_ui_action_type(self, mock_typewrite):
        """Test UI type action"""
        result = self.core.perform_ui_action(
            action_type="type",
            text="Hello World"
        )
        
        assert result['success'] is True
        assert result['action'] == 'type'
        mock_typewrite.assert_called_once_with("Hello World", interval=0.05)
    
    @patch('desk_pilot.core.pyautogui.press')
    def test_perform_ui_action_key(self, mock_press):
        """Test UI key press action"""
        result = self.core.perform_ui_action(
            action_type="key",
            key="enter"
        )
        
        assert result['success'] is True
        assert result['action'] == 'key'
        mock_press.assert_called_once_with("enter")
    
    @patch('desk_pilot.core.pyautogui.scroll')
    def test_perform_ui_action_scroll(self, mock_scroll):
        """Test UI scroll action"""
        result = self.core.perform_ui_action(
            action_type="scroll",
            x=100,
            y=200,
            clicks=3
        )
        
        assert result['success'] is True
        assert result['action'] == 'scroll'
        mock_scroll.assert_called_once_with(3, x=100, y=200)
    
    def test_perform_ui_action_invalid(self):
        """Test invalid UI action"""
        result = self.core.perform_ui_action(action_type="invalid")
        
        assert result['success'] is False
        assert 'error' in result
    
    @patch('desk_pilot.core.pygetwindow.getAllWindows')
    def test_get_window_list(self, mock_get_all_windows):
        """Test getting window list"""
        # Mock windows
        mock_window1 = Mock()
        mock_window1.title = "Window 1"
        mock_window1.left = 0
        mock_window1.top = 0
        mock_window1.width = 800
        mock_window1.height = 600
        mock_window1.isActive = False
        
        mock_window2 = Mock()
        mock_window2.title = "Window 2"
        mock_window2.left = 100
        mock_window2.top = 100
        mock_window2.width = 1024
        mock_window2.height = 768
        mock_window2.isActive = True
        
        mock_get_all_windows.return_value = [mock_window1, mock_window2]
        
        result = self.core.get_window_list()
        
        assert result['success'] is True
        assert len(result['windows']) == 2
        assert result['windows'][0]['title'] == "Window 1"
        assert result['windows'][1]['title'] == "Window 2"
        assert result['windows'][1]['is_active'] is True
    
    @patch('desk_pilot.core.pyautogui.size')
    def test_get_screen_size(self, mock_size):
        """Test getting screen size"""
        mock_size.return_value = (1920, 1080)
        
        result = self.core.get_screen_size()
        
        assert result['success'] is True
        assert result['width'] == 1920
        assert result['height'] == 1080
