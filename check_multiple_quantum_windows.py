#!/usr/bin/env python3
"""检查是否有多个QuantumSeek窗口"""

import pygetwindow as gw

def check_multiple_quantum_windows():
    """检查多个QuantumSeek窗口"""
    print('🔍 检查QuantumSeek窗口数量...')
    
    # 方法1: 精确匹配
    exact_windows = gw.getWindowsWithTitle('QuantumSeek - 量子文件搜索')
    print(f'\n📍 精确匹配 "QuantumSeek - 量子文件搜索": {len(exact_windows)} 个窗口')
    for i, window in enumerate(exact_windows):
        print(f'   {i+1}. 标题: "{window.title}"')
        print(f'      位置: ({window.left}, {window.top})')
        print(f'      尺寸: {window.width} x {window.height}')
        print(f'      状态: 激活={window.isActive}, 最小化={window.isMinimized}')
    
    # 方法2: 部分匹配
    partial_windows = gw.getWindowsWithTitle('QuantumSeek')
    print(f'\n📍 部分匹配 "QuantumSeek": {len(partial_windows)} 个窗口')
    for i, window in enumerate(partial_windows):
        print(f'   {i+1}. 标题: "{window.title}"')
        print(f'      位置: ({window.left}, {window.top})')
        print(f'      尺寸: {window.width} x {window.height}')
        print(f'      状态: 激活={window.isActive}, 最小化={window.isMinimized}')
    
    # 方法3: 模糊匹配
    all_windows = gw.getAllWindows()
    quantum_related = [w for w in all_windows if 'quantum' in w.title.lower()]
    print(f'\n📍 模糊匹配包含 "quantum": {len(quantum_related)} 个窗口')
    for i, window in enumerate(quantum_related):
        print(f'   {i+1}. 标题: "{window.title}"')
        print(f'      位置: ({window.left}, {window.top})')
        print(f'      尺寸: {window.width} x {window.height}')
        print(f'      状态: 激活={window.isActive}, 最小化={window.isMinimized}')
    
    # 检查是否有异常大的窗口
    print(f'\n🔍 检查异常大的窗口 (>2000像素):')
    large_windows = [w for w in all_windows if w.width > 2000 or w.height > 1500]
    for window in large_windows:
        if window.title.strip():  # 只显示有标题的窗口
            print(f'   标题: "{window.title}"')
            print(f'   位置: ({window.left}, {window.top})')
            print(f'   尺寸: {window.width} x {window.height}')
    
    # 分析结果
    print(f'\n📊 分析结果:')
    if len(exact_windows) > 1:
        print(f'   ⚠️ 发现多个精确匹配的QuantumSeek窗口')
        print(f'   💡 建议: 使用更精确的窗口选择逻辑')
    elif len(partial_windows) > 1:
        print(f'   ⚠️ 发现多个部分匹配的QuantumSeek窗口')
        print(f'   💡 建议: 使用完整标题进行匹配')
    elif len(exact_windows) == 1 and len(partial_windows) == 1:
        print(f'   ✅ 只有一个QuantumSeek窗口，窗口信息应该是准确的')
    else:
        print(f'   ❌ 未找到QuantumSeek窗口')
    
    return exact_windows, partial_windows, quantum_related

def main():
    """主函数"""
    try:
        exact, partial, related = check_multiple_quantum_windows()
        
        # 如果有多个窗口，建议解决方案
        if len(partial) > 1:
            print(f'\n💡 解决方案建议:')
            print(f'   1. 使用完整窗口标题进行匹配')
            print(f'   2. 优先选择激活的窗口')
            print(f'   3. 优先选择尺寸合理的窗口')
            print(f'   4. 添加窗口验证逻辑')
        
    except Exception as e:
        print(f'❌ 检查失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
