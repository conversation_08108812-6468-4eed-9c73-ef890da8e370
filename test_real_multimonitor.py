#!/usr/bin/env python3
"""测试真正的多显示器截图功能"""

from desk_pilot.core import DeskPilotCore
import base64
from PIL import Image
import io
import os

def test_real_multimonitor_screenshot():
    print('🔍 测试真正的多显示器截图功能...')

    # 创建核心实例
    core = DeskPilotCore(visual_mode=True)

    try:
        # 测试QuantumSeek窗口截图
        print('📸 正在截取QuantumSeek窗口（多显示器支持）...')
        result = core.capture_window('QuantumSeek')
        
        if isinstance(result, dict) and result.get('success'):
            base64_data = result['screenshot_base64']
            print('✅ 截图成功')
            print(f'📊 Base64长度: {len(base64_data)}')
            
            # 解码并保存截图
            image_data = base64.b64decode(base64_data)
            with open('quantum_multimonitor_screenshot.png', 'wb') as f:
                f.write(image_data)
            print('💾 截图已保存为: quantum_multimonitor_screenshot.png')
            
            # 分析图片内容
            img = Image.open(io.BytesIO(image_data))
            print(f'📏 截图尺寸: {img.size}')
            print(f'🎨 图片模式: {img.mode}')
            
            # 窗口信息
            window_info = result['window_info']
            print(f'🪟 QuantumSeek窗口信息:')
            print(f'   位置: ({window_info["left"]}, {window_info["top"]})')
            print(f'   尺寸: {window_info["width"]} x {window_info["height"]}')
            
            # 检查截图的主要颜色分布
            colors = img.getcolors(maxcolors=256*256*256)
            if colors:
                # 获取前5个最常见的颜色
                top_colors = sorted(colors, key=lambda x: x[0], reverse=True)[:5]
                print('\n🎨 截图中最常见的颜色:')
                for i, (count, color) in enumerate(top_colors):
                    percentage = (count / (img.size[0] * img.size[1])) * 100
                    print(f'   {i+1}. {color} - {percentage:.1f}%')
                    
                # 检查是否有紫色/粉色（QuantumSeek的主题色）
                has_purple = False
                purple_colors = []
                for count, (r, g, b) in top_colors:
                    # 检查紫色/粉色范围 - 更宽泛的检测
                    if ((r > 80 and b > 80 and r > g) or  # 紫色
                        (r > 120 and g < 100 and b > 80) or  # 粉色
                        (r > 100 and g > 50 and b > 100)):  # 其他紫色调
                        has_purple = True
                        purple_colors.append((r, g, b))
                
                if has_purple:
                    print(f'✅ 检测到紫色/粉色系颜色: {purple_colors}')
                    print('🎯 截图可能包含QuantumSeek界面！')
                else:
                    print('❌ 未检测到紫色/粉色，可能仍未包含QuantumSeek界面')
            
            # 检查文件大小
            file_size = os.path.getsize('quantum_multimonitor_screenshot.png')
            print(f'📁 截图文件大小: {file_size:,} 字节')
            
            # 与之前的截图对比
            if window_info['left'] >= 3840:
                print('\n🔧 多显示器处理分析:')
                print(f'   窗口位置: {window_info["left"]} (超出主屏宽度3840)')
                print('   使用了PIL ImageGrab的all_screens=True模式')
                if img.size[0] > 3840:
                    print(f'✅ 截图宽度 {img.size[0]} > 主屏宽度 3840，成功捕获多显示器')
                else:
                    print(f'⚠️ 截图宽度 {img.size[0]} 仍然只是主屏大小')
            
            print('\n🎯 请手动检查 quantum_multimonitor_screenshot.png')
            print('   应该能看到QuantumSeek的紫色界面，包括:')
            print('   - 左侧的搜索选项面板')
            print('   - 中央的搜索结果区域（显示0%进度）')
            print('   - 底部的统计信息（文件数量、大小等）')
            print('   - 紫色/粉色的UI主题色彩')
            
        else:
            print('❌ 截图失败')
            print(f'📄 错误信息: {result}')
            
    except Exception as e:
        print(f'❌ 测试失败: {str(e)}')
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_real_multimonitor_screenshot()
