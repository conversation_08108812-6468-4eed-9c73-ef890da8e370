#!/usr/bin/env python3
"""使用与test_quantum_current.py相同的方法测试DeskPilot"""

import logging
from desk_pilot.core import DeskPilotCore
import pygetwindow as gw
import base64
from PIL import Image

# 设置详细日志
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s:%(name)s:%(message)s')

def test_deskpilot_with_correct_method():
    """使用正确的窗口查找方法测试DeskPilot"""
    print('🔍 使用正确方法测试DeskPilot截图...')
    
    # 1. 使用与test_quantum_current.py相同的方法查找窗口
    print('\n📍 步骤1: 使用部分匹配查找窗口')
    windows = gw.getWindowsWithTitle('QuantumSeek')  # 与成功方法相同
    if not windows:
        print('❌ 未找到QuantumSeek窗口')
        return
    
    window = windows[0]
    print(f'✅ 找到窗口: {window.title}')
    print(f'📍 位置: ({window.left}, {window.top})')
    print(f'📏 尺寸: {window.width} x {window.height}')
    
    # 2. 直接调用DeskPilot，但传入部分标题
    print('\n📸 步骤2: 使用DeskPilot截图')
    core = DeskPilotCore(visual_mode=True)
    
    # 使用部分标题而不是完整标题
    result = core.capture_window('QuantumSeek')  # 部分匹配
    
    if result.get('success'):
        print('✅ DeskPilot截图成功')
        
        # 保存截图
        image_data = base64.b64decode(result['screenshot_base64'])
        filename = 'deskpilot_correct_method.png'
        with open(filename, 'wb') as f:
            f.write(image_data)
        print(f'💾 截图已保存: {filename}')
        
        # 分析截图
        img = Image.open(filename)
        pixels = list(img.getdata())
        total_pixels = len(pixels)
        non_black_pixels = sum(1 for p in pixels if sum(p[:3]) > 45)
        non_black_ratio = non_black_pixels / total_pixels
        
        print(f'\n🔍 截图分析:')
        print(f'   📏 尺寸: {img.size}')
        print(f'   📊 非黑色像素: {non_black_ratio:.2%}')
        
        if non_black_ratio > 0.4:
            print('   ✅ 截图质量良好')
        elif non_black_ratio > 0.1:
            print('   ⚠️ 截图质量一般')
        else:
            print('   ❌ 截图主要是黑色')
        
        return True
    else:
        print(f'❌ DeskPilot截图失败: {result.get("error")}')
        return False

def main():
    """主函数"""
    try:
        success = test_deskpilot_with_correct_method()
        
        if success:
            print('\n🎯 结论: 使用部分标题匹配可以解决问题')
        else:
            print('\n❌ 结论: 问题仍然存在，需要进一步调试')
            
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
