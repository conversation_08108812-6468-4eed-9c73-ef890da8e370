#!/usr/bin/env python3
"""检查QuantumSeek窗口的当前状态"""

import pygetwindow as gw

def check_quantum_window():
    print('🔍 检查QuantumSeek窗口状态...')
    
    try:
        # 查找QuantumSeek窗口
        windows = gw.getWindowsWithTitle('QuantumSeek')
        
        if not windows:
            print('❌ 未找到QuantumSeek窗口')
            # 列出所有窗口
            all_windows = gw.getAllWindows()
            print('\n📋 当前所有窗口:')
            for i, window in enumerate(all_windows[:20]):  # 只显示前20个
                if window.title.strip():
                    print(f'   {i+1}. {window.title} - ({window.left}, {window.top}) {window.width}x{window.height}')
            return None
        
        window = windows[0]
        print(f'✅ 找到QuantumSeek窗口: {window.title}')
        print(f'📍 位置: ({window.left}, {window.top})')
        print(f'📏 尺寸: {window.width} x {window.height}')
        print(f'🔍 状态: 最小化={window.isMinimized}, 最大化={window.isMaximized}, 激活={window.isActive}')
        
        # 检查窗口是否在副显示器上
        if window.left >= 3840:
            print('🖥️ 窗口在副显示器上')
        else:
            print('🖥️ 窗口在主显示器上')
        
        return window
        
    except Exception as e:
        print(f'❌ 检查窗口失败: {str(e)}')
        return None

if __name__ == '__main__':
    check_quantum_window()
