#!/usr/bin/env python3
"""测试主屏上的QuantumSeek截图"""

import logging
from desk_pilot.core import DeskPilotCore
import pygetwindow as gw
import base64
from PIL import Image

# 设置详细日志
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s:%(name)s:%(message)s')

def test_main_screen_quantum():
    """测试主屏上的QuantumSeek截图"""
    print('🖥️ 测试主屏QuantumSeek截图...')
    print('=' * 50)
    
    # 1. 检查QuantumSeek窗口状态
    print('\n📍 步骤1: 检查窗口状态')
    windows = gw.getWindowsWithTitle('QuantumSeek')
    if not windows:
        print('❌ 未找到QuantumSeek窗口')
        return False
    
    window = windows[0]
    print(f'✅ 找到窗口: {window.title}')
    print(f'📍 位置: ({window.left}, {window.top})')
    print(f'📏 尺寸: {window.width} x {window.height}')
    print(f'🔄 状态: 激活={window.isActive}, 最小化={window.isMinimized}')
    
    # 判断是否在主屏
    if window.left >= 0 and window.left < 1920:  # 假设主屏宽度1920
        print('✅ 窗口在主屏上')
    else:
        print('⚠️ 窗口可能不在主屏上')
    
    # 2. 使用DeskPilot截图
    print('\n📸 步骤2: DeskPilot截图测试')
    try:
        core = DeskPilotCore(visual_mode=True)
        result = core.capture_window('QuantumSeek')
        
        if result.get('success'):
            print('✅ DeskPilot截图成功')
            
            # 保存截图
            image_data = base64.b64decode(result['screenshot_base64'])
            filename = 'quantum_main_screen.png'
            with open(filename, 'wb') as f:
                f.write(image_data)
            print(f'💾 截图已保存: {filename}')
            
            # 分析截图质量
            img = Image.open(filename)
            pixels = list(img.getdata())
            total_pixels = len(pixels)
            non_black_pixels = sum(1 for p in pixels if sum(p[:3]) > 45)
            non_black_ratio = non_black_pixels / total_pixels
            
            print(f'\n🔍 截图分析:')
            print(f'   📏 尺寸: {img.size}')
            print(f'   📊 非黑色像素: {non_black_ratio:.2%}')
            
            if non_black_ratio > 0.4:
                print('   ✅ 截图质量: 良好')
                success = True
            elif non_black_ratio > 0.1:
                print('   ⚠️ 截图质量: 一般')
                success = True
            else:
                print('   ❌ 截图质量: 失败(主要是黑色)')
                success = False
            
            # 检测紫色主题
            purple_pixels = 0
            for pixel in pixels[:10000]:  # 检查前10000个像素
                r, g, b = pixel[:3]
                # 检测紫色/粉色范围
                if (r > 80 and b > 80 and r > g) or (r > 200 and g < 150 and b > 150):
                    purple_pixels += 1
            
            purple_ratio = purple_pixels / min(10000, total_pixels)
            print(f'   🎨 紫色像素: {purple_ratio:.2%}')
            
            if purple_ratio > 0.001:
                print('   ✅ 检测到QuantumSeek紫色主题')
            
            return success
            
        else:
            print(f'❌ DeskPilot截图失败: {result.get("error")}')
            return False
            
    except Exception as e:
        print(f'❌ 截图测试异常: {e}')
        import traceback
        traceback.print_exc()
        return False

def test_simple_method():
    """使用简单方法测试"""
    print('\n🔧 步骤3: 简单方法对比测试')
    
    try:
        import win32gui
        import win32ui
        import win32con
        
        # 获取窗口
        windows = gw.getWindowsWithTitle('QuantumSeek')
        if not windows:
            print('❌ 未找到窗口')
            return False
        
        window = windows[0]
        
        # 激活窗口
        if not window.isActive:
            window.activate()
            import time
            time.sleep(0.5)
        
        # 截图
        hdesktop = win32gui.GetDesktopWindow()
        desktop_dc = win32gui.GetWindowDC(hdesktop)
        img_dc = win32ui.CreateDCFromHandle(desktop_dc)
        mem_dc = img_dc.CreateCompatibleDC()

        screenshot = win32ui.CreateBitmap()
        screenshot.CreateCompatibleBitmap(img_dc, window.width, window.height)
        mem_dc.SelectObject(screenshot)

        mem_dc.BitBlt((0, 0), (window.width, window.height), img_dc, (window.left, window.top), win32con.SRCCOPY)

        bmpinfo = screenshot.GetInfo()
        bmpstr = screenshot.GetBitmapBits(True)

        img = Image.frombuffer(
            'RGB',
            (bmpinfo['bmWidth'], bmpinfo['bmHeight']),
            bmpstr, 'raw', 'BGRX', 0, 1
        )

        # 清理资源
        mem_dc.DeleteDC()
        img_dc.DeleteDC()
        win32gui.ReleaseDC(hdesktop, desktop_dc)
        win32gui.DeleteObject(screenshot.GetHandle())

        # 保存并分析
        filename = 'quantum_simple_method.png'
        img.save(filename)
        print(f'💾 简单方法截图: {filename}')
        
        pixels = list(img.getdata())
        total_pixels = len(pixels)
        non_black_pixels = sum(1 for p in pixels if sum(p[:3]) > 45)
        non_black_ratio = non_black_pixels / total_pixels
        
        print(f'   📏 尺寸: {img.size}')
        print(f'   📊 非黑色像素: {non_black_ratio:.2%}')
        
        if non_black_ratio > 0.4:
            print('   ✅ 简单方法成功')
            return True
        else:
            print('   ❌ 简单方法也失败')
            return False
            
    except Exception as e:
        print(f'❌ 简单方法异常: {e}')
        return False

def main():
    """主函数"""
    print('🎯 QuantumSeek主屏截图测试')
    print('请确保QuantumSeek窗口已移动到主屏并可见')
    
    # 测试DeskPilot方法
    deskpilot_success = test_main_screen_quantum()
    
    # 测试简单方法
    simple_success = test_simple_method()
    
    # 总结
    print('\n📋 测试总结:')
    print('=' * 30)
    print(f'DeskPilot方法: {"✅ 成功" if deskpilot_success else "❌ 失败"}')
    print(f'简单方法:     {"✅ 成功" if simple_success else "❌ 失败"}')
    
    if deskpilot_success:
        print('\n🎉 主屏截图成功！问题已解决')
    elif simple_success:
        print('\n⚠️ 简单方法成功，DeskPilot需要优化')
    else:
        print('\n❌ 两种方法都失败，需要进一步调试')

if __name__ == '__main__':
    main()
