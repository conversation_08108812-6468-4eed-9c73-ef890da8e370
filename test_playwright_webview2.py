#!/usr/bin/env python3
"""
测试Playwright WebView2自动化功能
"""

import logging
import time
from desk_pilot.playwright_webview2 import PlaywrightWebView2Manager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def main():
    """主测试函数"""
    print("🚀 开始测试Playwright WebView2自动化功能")
    print("=" * 60)
    
    # 创建管理器实例
    manager = PlaywrightWebView2Manager(visual_mode=True, operation_delay=1.0)
    
    try:
        # 1. 检测WebView2应用
        print("\n📱 步骤1: 检测系统中的WebView2应用")
        apps = manager.detect_webview2_apps()
        
        if not apps:
            print("❌ 未检测到任何WebView2应用")
            print("💡 请确保有WebView2应用正在运行，并且启用了调试端口")
            print("   例如：设置环境变量 WEBVIEW2_ADDITIONAL_BROWSER_ARGUMENTS=--remote-debugging-port=9222")
            return
        
        print(f"✅ 检测到 {len(apps)} 个潜在的WebView2应用:")
        for i, app in enumerate(apps, 1):
            print(f"   {i}. {app['name']} (PID: {app['pid']})")
            print(f"      调试端口: {app['debug_port'] if app['debug_port'] else '未设置'}")
            print(f"      命令行: {app['cmdline'][:80]}...")
            print()
        
        # 2. 选择有调试端口的应用进行连接
        target_app = None
        for app in apps:
            if app['debug_port']:
                target_app = app
                break
        
        if not target_app:
            print("❌ 未找到启用调试端口的WebView2应用")
            print("💡 请确保WebView2应用启动时设置了 --remote-debugging-port 参数")
            return
        
        print(f"🎯 选择应用: {target_app['name']} (端口: {target_app['debug_port']})")
        
        # 3. 连接到WebView2
        print(f"\n🔗 步骤2: 连接到WebView2应用 (端口: {target_app['debug_port']})")
        result = manager.connect_to_webview2(target_app['debug_port'])
        
        if not result['success']:
            print(f"❌ 连接失败: {result['error']}")
            return
        
        print("✅ 连接成功!")
        print(f"   当前URL: {result.get('url', 'N/A')}")
        print(f"   页面标题: {result.get('title', 'N/A')}")
        
        # 4. 获取状态信息
        print("\n📊 步骤3: 获取连接状态")
        status = manager.get_status()
        print(f"   连接状态: {'已连接' if status['connected'] else '未连接'}")
        print(f"   调试端口: {status.get('debug_port', 'N/A')}")
        print(f"   当前URL: {status.get('url', 'N/A')}")
        print(f"   页面标题: {status.get('title', 'N/A')}")
        print(f"   可视化模式: {'启用' if status.get('visual_mode') else '禁用'}")
        
        # 5. 捕获截图
        print("\n📸 步骤4: 捕获WebView2页面截图")
        screenshot_result = manager.capture_screenshot("playwright_webview2_test.png")
        
        if screenshot_result['success']:
            print(f"✅ 截图成功保存: {screenshot_result['screenshot_path']}")
        else:
            print(f"❌ 截图失败: {screenshot_result['error']}")
        
        # 6. 获取页面源码
        print("\n📄 步骤5: 获取页面HTML源码")
        source_result = manager.get_page_source()
        
        if source_result['success']:
            source_length = len(source_result['source'])
            print(f"✅ 成功获取页面源码 (长度: {source_length} 字符)")
            # 显示前200个字符作为预览
            preview = source_result['source'][:200].replace('\n', ' ').replace('\r', '')
            print(f"   预览: {preview}...")
        else:
            print(f"❌ 获取页面源码失败: {source_result['error']}")
        
        # 7. 执行JavaScript
        print("\n🔧 步骤6: 执行JavaScript代码")
        js_result = manager.execute_script("document.title")
        
        if js_result['success']:
            print(f"✅ JavaScript执行成功")
            print(f"   返回值: {js_result['result']}")
        else:
            print(f"❌ JavaScript执行失败: {js_result['error']}")
        
        # 8. 测试导航功能（如果当前页面支持）
        print("\n🌐 步骤7: 测试页面导航功能")
        current_url = status.get('url', '')
        
        if current_url and ('http' in current_url or 'file' in current_url):
            print(f"   当前URL: {current_url}")
            
            # 尝试刷新页面
            refresh_result = manager.execute_script("location.reload(); return 'Page refreshed';")
            if refresh_result['success']:
                print("✅ 页面刷新成功")
                time.sleep(2)  # 等待页面加载
            else:
                print(f"❌ 页面刷新失败: {refresh_result['error']}")
        else:
            print("   跳过导航测试（当前页面不是标准网页）")
        
        # 9. 测试元素查找和交互（基础测试）
        print("\n🖱️ 步骤8: 测试基础元素交互")
        
        # 尝试查找常见元素
        common_selectors = ['body', 'html', 'head', 'title']
        
        for selector in common_selectors:
            try:
                wait_result = manager.wait_for_element(selector, timeout=1000)
                if wait_result['success']:
                    print(f"✅ 找到元素: {selector}")
                    break
                else:
                    print(f"⚠️ 未找到元素: {selector}")
            except Exception as e:
                print(f"⚠️ 元素查找异常: {selector} - {e}")
        
        print("\n🎉 测试完成!")
        print("=" * 60)
        print("📋 测试总结:")
        print("   ✅ WebView2应用检测")
        print("   ✅ CDP连接建立")
        print("   ✅ 状态信息获取")
        print("   ✅ 页面截图捕获")
        print("   ✅ HTML源码获取")
        print("   ✅ JavaScript执行")
        print("   ✅ 基础元素交互")
        print("\n🚀 Playwright WebView2自动化功能测试成功!")
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理连接
        print("\n🔌 清理连接...")
        disconnect_result = manager.disconnect()
        if disconnect_result['success']:
            print("✅ 连接已断开")
        else:
            print(f"⚠️ 断开连接时出现问题: {disconnect_result.get('error', '未知错误')}")

if __name__ == "__main__":
    main()
