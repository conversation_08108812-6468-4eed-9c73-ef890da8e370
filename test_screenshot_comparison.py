#!/usr/bin/env python3
"""测试精准截图功能并进行对比分析"""

import logging
from desk_pilot.core import DeskPilotCore
import base64
from PIL import Image
import io
import time

# 设置详细日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')

def test_screenshot_comparison():
    print('🎯 测试DeskPilot精准截图功能')
    print('=' * 60)
    
    # 创建DeskPilot实例
    core = DeskPilotCore(visual_mode=True, operation_delay=0.5)
    
    # 1. 获取QuantumSeek窗口信息
    print('\n📍 步骤1: 获取QuantumSeek窗口信息')
    window_info = core.get_window_info('QuantumSeek')
    
    if not window_info:
        print('❌ 未找到QuantumSeek窗口')
        return
    
    print(f'✅ 窗口信息:')
    print(f'   标题: {window_info["title"]}')
    print(f'   位置: ({window_info["left"]}, {window_info["top"]})')
    print(f'   尺寸: {window_info["width"]} x {window_info["height"]}')
    print(f'   副显示器: {window_info["is_on_secondary_monitor"]}')
    print(f'   激活状态: {window_info["is_active"]}')
    print(f'   最小化: {window_info["is_minimized"]}')
    
    # 2. 执行精准截图
    print('\n📸 步骤2: 执行精准截图')
    print('正在截取QuantumSeek窗口...')
    
    result = core.capture_window('QuantumSeek', precise_region=True)
    
    if not result.get('success'):
        print(f'❌ 截图失败: {result.get("error")}')
        return
    
    print('✅ 截图成功！')
    
    # 3. 分析截图
    print('\n🔍 步骤3: 分析截图内容')
    
    # 解码截图
    image_data = base64.b64decode(result['screenshot_base64'])
    img = Image.open(io.BytesIO(image_data))
    
    print(f'📏 截图尺寸: {img.size}')
    print(f'📏 窗口尺寸: ({window_info["width"]}, {window_info["height"]})')
    
    # 验证尺寸匹配
    if img.size == (window_info["width"], window_info["height"]):
        print('✅ 截图尺寸与窗口尺寸完全匹配')
    else:
        print(f'⚠️ 截图尺寸不匹配')
    
    # 保存截图
    filename = 'deskpilot_quantum_screenshot.png'
    with open(filename, 'wb') as f:
        f.write(image_data)
    print(f'💾 截图已保存为: {filename}')
    
    # 4. 颜色分析
    print('\n🎨 步骤4: 颜色分析')
    
    colors = img.getcolors(maxcolors=256*256*256)
    if colors:
        # 获取主要颜色
        top_colors = sorted(colors, key=lambda x: x[0], reverse=True)[:10]
        
        print('主要颜色分布:')
        total_pixels = img.size[0] * img.size[1]
        
        for i, (count, color) in enumerate(top_colors):
            percentage = (count / total_pixels) * 100
            print(f'   {i+1:2d}. RGB{color} - {percentage:5.1f}% ({count:,} 像素)')
        
        # 检查是否有QuantumSeek特征颜色
        purple_colors = []
        dark_colors = []
        
        for count, (r, g, b) in top_colors:
            # 检查紫色系 (QuantumSeek的主题色)
            if 40 <= r <= 80 and 30 <= g <= 70 and 60 <= b <= 120:
                purple_colors.append((count, (r, g, b)))
            # 检查深色背景
            elif r < 50 and g < 50 and b < 50:
                dark_colors.append((count, (r, g, b)))
        
        print(f'\n🔍 特征颜色检测:')
        if purple_colors:
            print(f'   ✅ 检测到 {len(purple_colors)} 种紫色系颜色 (QuantumSeek主题)')
            for count, color in purple_colors[:3]:
                percentage = (count / total_pixels) * 100
                print(f'      RGB{color} - {percentage:.1f}%')
        else:
            print('   ⚠️ 未检测到明显的紫色主题色')
        
        if dark_colors:
            print(f'   ✅ 检测到 {len(dark_colors)} 种深色背景')
        
        # 检查是否主要是黑色（可能表示截图失败）
        if top_colors[0][1] == (0, 0, 0) and top_colors[0][0] > total_pixels * 0.8:
            print('   ⚠️ 截图主要是黑色，可能窗口被遮挡或截图失败')
        else:
            print('   ✅ 截图包含有效的彩色内容')
    
    # 5. 与用户提供的截图进行基本对比
    print('\n📊 步骤5: 基本对比分析')
    print('根据用户提供的手动截图，QuantumSeek应该包含以下特征:')
    print('   • 紫色主题界面')
    print('   • 左侧导航栏 (搜索、历史、导入、设置)')
    print('   • 中央搜索区域')
    print('   • 底部统计信息 (0个搜索结果、0s搜索时间等)')
    print('   • 右上角功能按钮')
    
    # 基于颜色分析进行初步判断
    has_purple_theme = len(purple_colors) > 0
    has_dark_background = len(dark_colors) > 0
    not_mostly_black = not (top_colors[0][1] == (0, 0, 0) and top_colors[0][0] > total_pixels * 0.8)
    
    match_score = 0
    if has_purple_theme:
        match_score += 1
        print('   ✅ 检测到紫色主题')
    else:
        print('   ❌ 未检测到紫色主题')
    
    if has_dark_background:
        match_score += 1
        print('   ✅ 检测到深色背景')
    else:
        print('   ❌ 未检测到深色背景')
    
    if not_mostly_black:
        match_score += 1
        print('   ✅ 截图包含有效内容')
    else:
        print('   ❌ 截图主要是黑色')
    
    # 6. 总结
    print('\n📋 步骤6: 截图测试总结')
    print('=' * 60)
    
    print(f'📸 截图文件: {filename}')
    print(f'📏 截图尺寸: {img.size}')
    print(f'🎯 特征匹配: {match_score}/3')
    
    if match_score >= 2:
        print('✅ 初步判断: 截图可能与用户提供的手动截图相似')
        print('🔍 建议: 请用户进一步确认截图内容是否一致')
    else:
        print('⚠️ 初步判断: 截图可能与用户提供的手动截图不一致')
        print('🔧 建议: 需要检查窗口状态或截图方法')
    
    print(f'\n💡 请查看保存的截图文件 "{filename}" 进行详细对比')

if __name__ == '__main__':
    test_screenshot_comparison()
