{"name": "deskpilot-ts", "version": "0.1.0", "description": "DeskPilot TypeScript implementation - MCP server for Windows desktop automation", "main": "dist/index.js", "bin": {"deskpilot-ts": "dist/index.js"}, "scripts": {"build": "tsc", "dev": "tsx src/index.ts", "start": "node dist/index.js", "debug": "tsx src/index.ts --debug", "test": "jest", "clean": "<PERSON><PERSON><PERSON> dist", "mcp": "node dist/index.js --debug", "install-global": "npm install -g .", "link": "npm link"}, "keywords": ["mcp", "desktop-automation", "tauri", "windows", "screenshot", "ui-automation"], "author": "DeskPilot Team", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.0.0", "commander": "^12.0.0"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0", "tsx": "^4.0.0", "rimraf": "^5.0.0", "jest": "^29.0.0", "@types/jest": "^29.0.0"}, "engines": {"node": ">=18.0.0"}, "os": ["win32"]}