#!/usr/bin/env python3
"""测试实时获取窗口坐标"""

import logging
from desk_pilot.core import DeskPilotCore
import pygetwindow as gw
import time

# 设置详细日志
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s:%(name)s:%(message)s')

def test_fresh_coordinates():
    print('🔍 测试实时获取窗口坐标...')
    
    # 每次都重新获取窗口信息
    print('\n📍 第一次获取窗口信息:')
    windows = gw.getWindowsWithTitle('QuantumSeek')
    if windows:
        window = windows[0]
        print(f'   位置: ({window.left}, {window.top})')
        print(f'   尺寸: {window.width} x {window.height}')
        print(f'   激活状态: {window.isActive}')
    
    # 等待一下
    time.sleep(1)
    
    # 再次获取
    print('\n📍 第二次获取窗口信息:')
    windows = gw.getWindowsWithTitle('QuantumSeek')
    if windows:
        window = windows[0]
        print(f'   位置: ({window.left}, {window.top})')
        print(f'   尺寸: {window.width} x {window.height}')
        print(f'   激活状态: {window.isActive}')
        
        # 激活窗口
        if not window.isActive:
            print('   激活窗口...')
            window.activate()
            time.sleep(0.5)
        
        # 第三次获取（激活后）
        print('\n📍 激活后获取窗口信息:')
        windows = gw.getWindowsWithTitle('QuantumSeek')
        if windows:
            window = windows[0]
            print(f'   位置: ({window.left}, {window.top})')
            print(f'   尺寸: {window.width} x {window.height}')
            print(f'   激活状态: {window.isActive}')
    
    # 现在使用DeskPilot
    print('\n🔧 使用DeskPilot截图...')
    core = DeskPilotCore(visual_mode=False)
    result = core.capture_window('QuantumSeek')
    
    if result.get('success'):
        window_info = result['window_info']
        print(f'✅ DeskPilot获取的坐标: ({window_info["left"]}, {window_info["top"]})')
        print(f'📏 DeskPilot获取的尺寸: {window_info["width"]} x {window_info["height"]}')
        
        # 分析截图
        import base64
        from PIL import Image
        import io
        
        image_data = base64.b64decode(result['screenshot_base64'])
        img = Image.open(io.BytesIO(image_data))
        print(f'📸 截图尺寸: {img.size}')
        
        # 保存截图
        with open('fresh_coordinates_test.png', 'wb') as f:
            f.write(image_data)
        print('💾 截图保存为: fresh_coordinates_test.png')
        
        # 检查颜色
        colors = img.getcolors(maxcolors=256*256*256)
        if colors:
            top_colors = sorted(colors, key=lambda x: x[0], reverse=True)[:3]
            print('\n🎨 主要颜色:')
            for i, (count, color) in enumerate(top_colors):
                percentage = (count / (img.size[0] * img.size[1])) * 100
                print(f'   {i+1}. {color} - {percentage:.1f}%')
                
            # 检查是否主要是黑色
            if top_colors[0][1] == (0, 0, 0) and top_colors[0][0] > img.size[0] * img.size[1] * 0.8:
                print('⚠️ 截图主要是黑色，窗口可能被遮挡或内容为空')
            else:
                print('✅ 截图包含非黑色内容')
    else:
        print(f'❌ 截图失败: {result.get("error")}')

if __name__ == '__main__':
    test_fresh_coordinates()
