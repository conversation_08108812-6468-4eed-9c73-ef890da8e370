#!/usr/bin/env python3
"""使用Windows API测试真正的多显示器截图"""

import win32gui
import win32ui
import win32con
import win32api
from PIL import Image
import base64
import io

def capture_all_monitors():
    """使用Windows API捕获所有显示器"""
    print('🔍 使用Windows API测试多显示器截图...')
    
    try:
        # 获取虚拟屏幕的尺寸（包含所有显示器）
        left = win32api.GetSystemMetrics(win32con.SM_XVIRTUALSCREEN)
        top = win32api.GetSystemMetrics(win32con.SM_YVIRTUALSCREEN)
        width = win32api.GetSystemMetrics(win32con.SM_CXVIRTUALSCREEN)
        height = win32api.GetSystemMetrics(win32con.SM_CYVIRTUALSCREEN)
        
        print(f'🖥️ 虚拟屏幕区域: left={left}, top={top}, width={width}, height={height}')
        
        # 创建设备上下文
        hdesktop = win32gui.GetDesktopWindow()
        desktop_dc = win32gui.GetWindowDC(hdesktop)
        img_dc = win32ui.CreateDCFromHandle(desktop_dc)
        mem_dc = img_dc.CreateCompatibleDC()
        
        # 创建位图
        screenshot = win32ui.CreateBitmap()
        screenshot.CreateCompatibleBitmap(img_dc, width, height)
        mem_dc.SelectObject(screenshot)
        
        # 复制屏幕内容到位图
        mem_dc.BitBlt((0, 0), (width, height), img_dc, (left, top), win32con.SRCCOPY)
        
        # 获取位图数据
        bmpinfo = screenshot.GetInfo()
        bmpstr = screenshot.GetBitmapBits(True)
        
        # 转换为PIL Image
        img = Image.frombuffer(
            'RGB',
            (bmpinfo['bmWidth'], bmpinfo['bmHeight']),
            bmpstr, 'raw', 'BGRX', 0, 1
        )
        
        # 清理资源
        mem_dc.DeleteDC()
        img_dc.DeleteDC()
        win32gui.ReleaseDC(hdesktop, desktop_dc)
        win32gui.DeleteObject(screenshot.GetHandle())
        
        print(f'✅ 成功捕获多显示器截图: {img.size}')
        
        # 保存截图
        img.save('win32_multimonitor_screenshot.png')
        print('💾 截图已保存为: win32_multimonitor_screenshot.png')
        
        # 分析颜色
        colors = img.getcolors(maxcolors=256*256*256)
        if colors:
            top_colors = sorted(colors, key=lambda x: x[0], reverse=True)[:5]
            print('\n🎨 截图中最常见的颜色:')
            for i, (count, color) in enumerate(top_colors):
                percentage = (count / (img.size[0] * img.size[1])) * 100
                print(f'   {i+1}. {color} - {percentage:.1f}%')
                
            # 检查紫色/粉色
            has_purple = False
            for count, (r, g, b) in top_colors:
                if ((r > 80 and b > 80 and r > g) or 
                    (r > 120 and g < 100 and b > 80) or 
                    (r > 100 and g > 50 and b > 100)):
                    has_purple = True
                    print(f'✅ 检测到紫色/粉色: {(r, g, b)}')
                    break
            
            if not has_purple:
                print('❌ 未检测到紫色/粉色')
        
        return img
        
    except Exception as e:
        print(f'❌ Windows API截图失败: {str(e)}')
        import traceback
        traceback.print_exc()
        return None

def test_quantum_window_region():
    """测试QuantumSeek窗口区域截图"""
    print('\n🎯 测试QuantumSeek窗口区域截图...')
    
    # QuantumSeek窗口信息（从之前的测试获得）
    window_left = 5849
    window_top = 65
    window_width = 2723
    window_height = 1514
    
    try:
        # 使用Windows API截取特定区域
        hdesktop = win32gui.GetDesktopWindow()
        desktop_dc = win32gui.GetWindowDC(hdesktop)
        img_dc = win32ui.CreateDCFromHandle(desktop_dc)
        mem_dc = img_dc.CreateCompatibleDC()
        
        # 创建位图
        screenshot = win32ui.CreateBitmap()
        screenshot.CreateCompatibleBitmap(img_dc, window_width, window_height)
        mem_dc.SelectObject(screenshot)
        
        # 复制窗口区域到位图
        mem_dc.BitBlt((0, 0), (window_width, window_height), img_dc, (window_left, window_top), win32con.SRCCOPY)
        
        # 获取位图数据
        bmpinfo = screenshot.GetInfo()
        bmpstr = screenshot.GetBitmapBits(True)
        
        # 转换为PIL Image
        img = Image.frombuffer(
            'RGB',
            (bmpinfo['bmWidth'], bmpinfo['bmHeight']),
            bmpstr, 'raw', 'BGRX', 0, 1
        )
        
        # 清理资源
        mem_dc.DeleteDC()
        img_dc.DeleteDC()
        win32gui.ReleaseDC(hdesktop, desktop_dc)
        win32gui.DeleteObject(screenshot.GetHandle())
        
        print(f'✅ 成功捕获QuantumSeek窗口: {img.size}')
        
        # 保存截图
        img.save('quantum_window_win32.png')
        print('💾 QuantumSeek窗口截图已保存为: quantum_window_win32.png')
        
        # 分析颜色
        colors = img.getcolors(maxcolors=256*256*256)
        if colors:
            top_colors = sorted(colors, key=lambda x: x[0], reverse=True)[:5]
            print('\n🎨 QuantumSeek窗口中最常见的颜色:')
            for i, (count, color) in enumerate(top_colors):
                percentage = (count / (img.size[0] * img.size[1])) * 100
                print(f'   {i+1}. {color} - {percentage:.1f}%')
                
            # 检查紫色/粉色
            has_purple = False
            purple_colors = []
            for count, (r, g, b) in top_colors:
                if ((r > 80 and b > 80 and r > g) or 
                    (r > 120 and g < 100 and b > 80) or 
                    (r > 100 and g > 50 and b > 100)):
                    has_purple = True
                    purple_colors.append((r, g, b))
            
            if has_purple:
                print(f'✅ 检测到QuantumSeek的紫色主题: {purple_colors}')
                print('🎯 成功捕获QuantumSeek界面！')
            else:
                print('❌ 未检测到紫色主题，可能截图区域不正确')
        
        return img
        
    except Exception as e:
        print(f'❌ QuantumSeek窗口截图失败: {str(e)}')
        import traceback
        traceback.print_exc()
        return None

if __name__ == '__main__':
    # 测试完整多显示器截图
    full_screenshot = capture_all_monitors()
    
    # 测试QuantumSeek窗口区域截图
    quantum_screenshot = test_quantum_window_region()
    
    if quantum_screenshot:
        print('\n🎉 Windows API多显示器截图测试成功！')
        print('📸 请检查生成的截图文件:')
        print('   - win32_multimonitor_screenshot.png (完整多显示器)')
        print('   - quantum_window_win32.png (QuantumSeek窗口)')
    else:
        print('\n❌ 测试失败')
