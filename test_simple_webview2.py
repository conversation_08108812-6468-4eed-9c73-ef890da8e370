#!/usr/bin/env python3
"""
测试简化WebView2自动化功能
"""

import logging
import time
from desk_pilot.simple_webview2 import SimpleWebView2Manager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def main():
    """主测试函数"""
    print("🚀 开始测试简化WebView2自动化功能")
    print("=" * 60)
    
    # 创建管理器实例
    manager = SimpleWebView2Manager(visual_mode=True, operation_delay=1.0)
    
    try:
        # 1. 检测WebView2应用
        print("\n📱 步骤1: 检测系统中的WebView2应用")
        apps = manager.detect_webview2_apps()
        
        if not apps:
            print("❌ 未检测到任何WebView2应用")
            print("💡 请确保有WebView2应用正在运行，并且启用了调试端口")
            return
        
        print(f"✅ 检测到 {len(apps)} 个潜在的WebView2应用:")
        for i, app in enumerate(apps, 1):
            print(f"   {i}. {app['name']} (PID: {app['pid']})")
            print(f"      调试端口: {app['debug_port'] if app['debug_port'] else '未设置'}")
            print(f"      命令行: {app['cmdline'][:80]}...")
            print()
        
        # 2. 选择有调试端口的应用进行连接
        target_app = None
        for app in apps:
            if app['debug_port']:
                target_app = app
                break
        
        if not target_app:
            print("❌ 未找到启用调试端口的WebView2应用")
            print("💡 请确保WebView2应用启动时设置了 --remote-debugging-port 参数")
            return
        
        print(f"🎯 选择应用: {target_app['name']} (端口: {target_app['debug_port']})")
        
        # 3. 连接到WebView2
        print(f"\n🔗 步骤2: 连接到WebView2应用 (端口: {target_app['debug_port']})")
        result = manager.connect_to_webview2(target_app['debug_port'])
        
        if not result['success']:
            print(f"❌ 连接失败: {result['error']}")
            return
        
        print("✅ 连接成功!")
        print(f"   页面ID: {result.get('page_id', 'N/A')}")
        print(f"   当前URL: {result.get('url', 'N/A')}")
        print(f"   页面标题: {result.get('title', 'N/A')}")
        
        # 4. 获取状态信息
        print("\n📊 步骤3: 获取连接状态")
        status = manager.get_status()
        print(f"   连接状态: {'已连接' if status['connected'] else '未连接'}")
        print(f"   调试端口: {status.get('debug_port', 'N/A')}")
        print(f"   页面ID: {status.get('page_id', 'N/A')}")
        print(f"   当前URL: {status.get('url', 'N/A')}")
        print(f"   页面标题: {status.get('title', 'N/A')}")
        print(f"   可视化模式: {'启用' if status.get('visual_mode') else '禁用'}")
        
        # 5. 捕获截图
        print("\n📸 步骤4: 捕获WebView2页面截图")
        screenshot_result = manager.capture_screenshot("simple_webview2_test.png")
        
        if screenshot_result['success']:
            print(f"✅ 截图成功保存: {screenshot_result['screenshot_path']}")
        else:
            print(f"❌ 截图失败: {screenshot_result['error']}")
        
        # 6. 获取页面源码
        print("\n📄 步骤5: 获取页面HTML源码")
        source_result = manager.get_page_source()
        
        if source_result['success']:
            source_length = len(source_result['source'])
            print(f"✅ 成功获取页面源码 (长度: {source_length} 字符)")
            # 显示前200个字符作为预览
            preview = source_result['source'][:200].replace('\n', ' ').replace('\r', '')
            print(f"   预览: {preview}...")
        else:
            print(f"❌ 获取页面源码失败: {source_result['error']}")
        
        # 7. 执行JavaScript
        print("\n🔧 步骤6: 执行JavaScript代码")
        
        # 测试多个JavaScript命令
        js_tests = [
            ("document.title", "获取页面标题"),
            ("location.href", "获取当前URL"),
            ("document.body ? 'Body exists' : 'No body'", "检查body元素"),
            ("navigator.userAgent", "获取用户代理"),
            ("window.innerWidth + 'x' + window.innerHeight", "获取窗口尺寸")
        ]
        
        for script, description in js_tests:
            js_result = manager.execute_script(script)
            if js_result['success']:
                print(f"   ✅ {description}: {js_result['result']}")
            else:
                print(f"   ❌ {description}: {js_result['error']}")
        
        # 8. 测试页面交互
        print("\n🖱️ 步骤7: 测试页面交互")
        
        # 尝试获取页面元素信息
        element_tests = [
            ("document.querySelectorAll('*').length", "页面元素总数"),
            ("document.querySelectorAll('a').length", "链接数量"),
            ("document.querySelectorAll('img').length", "图片数量"),
            ("document.querySelectorAll('input').length", "输入框数量"),
            ("document.querySelectorAll('button').length", "按钮数量")
        ]
        
        for script, description in element_tests:
            result = manager.execute_script(script)
            if result['success']:
                print(f"   ✅ {description}: {result['result']}")
            else:
                print(f"   ⚠️ {description}: 无法获取")
        
        # 9. 测试页面滚动
        print("\n📜 步骤8: 测试页面滚动")
        
        # 获取当前滚动位置
        scroll_result = manager.execute_script("window.pageYOffset")
        if scroll_result['success']:
            original_scroll = scroll_result['result']
            print(f"   当前滚动位置: {original_scroll}px")
            
            # 尝试滚动页面
            scroll_down = manager.execute_script("window.scrollBy(0, 100); return window.pageYOffset;")
            if scroll_down['success']:
                print(f"   滚动后位置: {scroll_down['result']}px")
                
                # 滚动回原位置
                manager.execute_script(f"window.scrollTo(0, {original_scroll});")
                print("   ✅ 页面滚动测试成功")
            else:
                print("   ⚠️ 页面滚动失败")
        else:
            print("   ⚠️ 无法获取滚动位置")
        
        print("\n🎉 测试完成!")
        print("=" * 60)
        print("📋 测试总结:")
        print("   ✅ WebView2应用检测")
        print("   ✅ 直接CDP连接建立")
        print("   ✅ 状态信息获取")
        print("   ✅ 页面截图捕获")
        print("   ✅ HTML源码获取")
        print("   ✅ JavaScript执行")
        print("   ✅ 页面元素分析")
        print("   ✅ 页面交互测试")
        print("\n🚀 简化WebView2自动化功能测试成功!")
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理连接
        print("\n🔌 清理连接...")
        disconnect_result = manager.disconnect()
        if disconnect_result['success']:
            print("✅ 连接已断开")
        else:
            print(f"⚠️ 断开连接时出现问题: {disconnect_result.get('error', '未知错误')}")

if __name__ == "__main__":
    main()
