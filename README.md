# 🚁 DeskPilot

A Model Context Protocol (MCP) tool for Windows desktop automation.

## 🎯 Overview

DeskPilot enables AI assistants to interact with Windows desktop applications through the Model Context Protocol. It provides tools for:

- 📸 **Window Screenshot Capture** - Capture screenshots of specific windows or the active window
- 🖱️ **UI Automation** - Perform clicks, typing, key presses, and scrolling
- 🪟 **Window Management** - List and manage application windows
- 🎨 **Visual Mode** - Real-time operation effects for development and debugging
- 💻 **IDE Terminal Automation** - Automated IDE workflow for development tasks

## 🚀 Quick Start

### Prerequisites

- Python 3.12 or higher
- Windows operating system
- [uv](https://docs.astral.sh/uv/) package manager

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd desk-pilot
```

2. Install dependencies:
```bash
uv sync
```

3. Install the package:
```bash
uv pip install -e .
```

### Usage

Start the MCP server:
```bash
desk-pilot
```

Or with debug logging:
```bash
desk-pilot --debug
```

Disable visual mode (visual mode is enabled by default):
```bash
desk-pilot --no-visual-mode
```

Custom operation delay in visual mode:
```bash
desk-pilot --operation-delay 1.0
```

## 🔧 Configuration

### Claude Desktop

Add to your Claude Desktop configuration file (`~/.claude/claude_desktop_config.json`):

```json
{
  "mcpServers": {
    "deskPilot": {
      "command": "desk-pilot",
      "args": ["--debug"]
    }
  }
}
```

### Other MCP Clients

DeskPilot works with any MCP-compatible client. Use the command `desk-pilot` as the server executable.

## 🛠️ Available Tools

### `capture-window`
Capture a screenshot of a specific window or the active window.

**Parameters:**
- `window_title` (optional): Title of the window to capture

**Example:**
```json
{
  "name": "capture-window",
  "arguments": {
    "window_title": "Notepad"
  }
}
```

### `ui-action`
Perform UI actions like clicking, typing, key presses, or scrolling.

**Parameters:**
- `action_type`: Type of action ("click", "type", "key", "scroll")
- `x`, `y`: Coordinates (for click/scroll)
- `text`: Text to type (for type action)
- `key`: Key to press (for key action)
- `button`: Mouse button ("left", "right", "middle")
- `clicks`: Number of clicks/scroll steps
- `interval`: Typing interval

**Examples:**
```json
{
  "name": "ui-action",
  "arguments": {
    "action_type": "click",
    "x": 100,
    "y": 200
  }
}
```

```json
{
  "name": "ui-action", 
  "arguments": {
    "action_type": "type",
    "text": "Hello, World!"
  }
}
```

### `list-windows`
Get a list of all visible windows with their properties.

### `get-screen-size`
Get the current screen dimensions.



### `set-visual-mode`
Enable or disable visual mode for real-time operation effects.

**Parameters:**
- `enabled`: Whether to enable visual mode
- `operation_delay` (optional): Delay between operations in seconds (default: 0.5)

### `get-mouse-position`
Get current mouse cursor position.

**Parameters:** None

### `automate-ide-terminal`
Automate IDE terminal workflow: find IDE window, open terminal, and execute command.

**Parameters:**
- `ide_window_title`: IDE window title or partial title to search for
- `command`: Command to execute in the terminal

## 🎨 Visual Mode

DeskPilot **enables visual mode by default** to show real-time operation effects, perfect for development, debugging, and demonstrations.

**Features:**
- Real-time mouse movement visualization
- Operation step logging
- Configurable delays between operations
- IDE terminal automation workflow

**Usage:**
```bash
# Visual mode is enabled by default
desk-pilot

# Disable visual mode for faster operations
desk-pilot --no-visual-mode

# Custom delay for slower demonstration
desk-pilot --operation-delay 2.0
```

## 🏗️ Development

### Project Structure

```
desk-pilot/
├── desk_pilot/
│   ├── __init__.py          # Package initialization
│   ├── __main__.py          # Command line entry point
│   ├── core.py              # Core desktop automation
│   ├── mcp_server.py        # MCP server implementation

├── docs/
│   └── desk_pilot_design.md # Design documentation
├── tests/                   # Test files
├── pyproject.toml          # Project configuration
└── README.md               # This file
```

### Running Tests

```bash
uv run pytest
```

### Code Formatting

```bash
uv run black desk_pilot/
uv run isort desk_pilot/
```

## 📋 Requirements

### System Requirements
- Windows 10/11
- Python 3.12+
- Screen resolution: Any (automatically detected)



## 🔒 Security Considerations

DeskPilot can perform UI automation and access your desktop. When using with AI assistants:

- Review AI requests before allowing desktop interactions
- Use in trusted environments only
- Be cautious with sensitive applications
- Consider running in a virtual machine for testing

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.

## 🆘 Support

- 📖 [Documentation](docs/)
- 🐛 [Issue Tracker](https://github.com/your-username/desk-pilot/issues)
- 💬 [Discussions](https://github.com/your-username/desk-pilot/discussions)

## 🙏 Acknowledgments

- [Model Context Protocol](https://modelcontextprotocol.io/) by Anthropic
- [PyAutoGUI](https://pyautogui.readthedocs.io/) for desktop automation

