#!/usr/bin/env python3
"""分析method1_deskpilot.png截图"""

from PIL import Image

def analyze_method1_screenshot():
    """分析DeskPilot方法生成的截图"""
    filename = "quantum_current_test.png"
    print(f'🔍 分析DeskPilot截图: {filename}')
    
    try:
        with Image.open(filename) as img:
            width, height = img.size
            print(f'  图片尺寸: {width} x {height}')
            
            # 获取像素数据
            pixels = list(img.getdata())
            total_pixels = len(pixels)
            
            # 详细分析颜色分布
            if img.mode in ['RGB', 'RGBA']:
                r_sum = g_sum = b_sum = 0
                purple_pixels = 0
                dark_pixels = 0
                bright_pixels = 0
                
                # 颜色分布统计
                color_ranges = {
                    'very_dark': 0,    # 0-30
                    'dark': 0,         # 31-80
                    'medium': 0,       # 81-150
                    'bright': 0,       # 151-255
                }
                
                for pixel in pixels:
                    r, g, b = pixel[:3]
                    r_sum += r
                    g_sum += g
                    b_sum += b
                    
                    brightness = (r + g + b) / 3
                    
                    # 亮度分类
                    if brightness <= 30:
                        color_ranges['very_dark'] += 1
                    elif brightness <= 80:
                        color_ranges['dark'] += 1
                    elif brightness <= 150:
                        color_ranges['medium'] += 1
                    else:
                        color_ranges['bright'] += 1
                    
                    # 检查紫色系像素
                    if b > 100 and b > r and b > g:
                        purple_pixels += 1
                    
                    # 检查特定颜色
                    if brightness < 50:
                        dark_pixels += 1
                    elif brightness > 150:
                        bright_pixels += 1
                
                # 计算平均颜色
                avg_r = r_sum / total_pixels
                avg_g = g_sum / total_pixels
                avg_b = b_sum / total_pixels
                avg_brightness = (avg_r + avg_g + avg_b) / 3
                
                print(f'  平均颜色: R={avg_r:.1f}, G={avg_g:.1f}, B={avg_b:.1f}')
                print(f'  平均亮度: {avg_brightness:.1f}')
                
                # 亮度分布
                print(f'\n  🎨 亮度分布:')
                for range_name, count in color_ranges.items():
                    percentage = count / total_pixels * 100
                    print(f'    {range_name}: {count} ({percentage:.1f}%)')
                
                # 特殊颜色检测
                purple_ratio = purple_pixels / total_pixels
                print(f'\n  🟣 紫色像素: {purple_pixels} ({purple_ratio:.2%})')
                
                # 检查是否有UI元素的特征
                print(f'\n  🔍 UI特征检测:')
                
                # 检查是否有文本特征（高对比度相邻像素）
                text_indicators = 0
                for y in range(1, height - 1):
                    for x in range(1, width - 1):
                        if x < width and y < height:
                            center_pixel = img.getpixel((x, y))
                            center_brightness = sum(center_pixel[:3]) / 3
                            
                            # 检查周围像素的对比度
                            neighbors = [
                                img.getpixel((x-1, y)),
                                img.getpixel((x+1, y)),
                                img.getpixel((x, y-1)),
                                img.getpixel((x, y+1))
                            ]
                            
                            for neighbor in neighbors:
                                neighbor_brightness = sum(neighbor[:3]) / 3
                                if abs(center_brightness - neighbor_brightness) > 100:
                                    text_indicators += 1
                                    break
                
                text_density = text_indicators / total_pixels
                print(f'    文本/边缘密度: {text_density:.4f}')
                
                if text_density > 0.01:
                    print('    ✅ 检测到丰富的文本/UI元素')
                elif text_density > 0.005:
                    print('    ℹ️ 检测到一些文本/UI元素')
                else:
                    print('    ⚠️ 文本/UI元素较少')
                
                # 区域分析
                print(f'\n  📍 区域详细分析:')
                analyze_specific_regions(img)
                
    except Exception as e:
        print(f'  ❌ 分析失败: {e}')

def analyze_specific_regions(img):
    """分析图像的特定区域"""
    width, height = img.size
    
    regions = {
        '左上角': (0, 0, width//4, height//4),
        '右上角': (3*width//4, 0, width, height//4),
        '左下角': (0, 3*height//4, width//4, height),
        '右下角': (3*width//4, 3*height//4, width, height),
        '中央': (width//4, height//4, 3*width//4, 3*height//4),
        '顶部条': (0, 0, width, height//10),
        '底部条': (0, 9*height//10, width, height),
        '左侧条': (0, 0, width//10, height),
        '右侧条': (9*width//10, 0, width, height),
    }
    
    for region_name, (x1, y1, x2, y2) in regions.items():
        try:
            region = img.crop((x1, y1, x2, y2))
            pixels = list(region.getdata())
            
            if not pixels:
                continue
                
            total = len(pixels)
            r_sum = g_sum = b_sum = 0
            non_black = 0
            
            for pixel in pixels:
                r, g, b = pixel[:3]
                r_sum += r
                g_sum += g
                b_sum += b
                
                if r > 20 or g > 20 or b > 20:
                    non_black += 1
            
            avg_brightness = (r_sum + g_sum + b_sum) / (3 * total)
            non_black_ratio = non_black / total
            
            print(f'    {region_name}: 亮度={avg_brightness:.1f}, 非黑色={non_black_ratio:.2%}')
            
        except Exception as e:
            print(f'    {region_name}: 分析失败 - {e}')

def compare_with_reference():
    """与参考截图进行比较"""
    print(f'\n📋 与用户参考截图比较:')
    print(f'  根据用户提供的QuantumSeek截图，应该看到:')
    print(f'  ✅ 深色主题背景')
    print(f'  ✅ 紫色主题元素')
    print(f'  ✅ 左侧菜单栏（搜索、历史、下载、设置）')
    print(f'  ✅ 顶部搜索区域')
    print(f'  ✅ 中央进度显示')
    print(f'  ✅ 底部状态信息')
    print(f'  ✅ 清晰的UI文本和边界')
    
    print(f'\n  🔍 当前截图分析结果:')
    print(f'  - 如果平均亮度很低（<20）且非黑色像素>40%，说明是深色主题界面')
    print(f'  - 如果检测到紫色像素>0.1%，说明有紫色主题元素')
    print(f'  - 如果各区域亮度有差异，说明有UI结构')
    print(f'  - 如果文本密度>0.005，说明有文本内容')

def main():
    """主分析函数"""
    print('🔬 DeskPilot截图详细分析')
    print('=' * 50)
    
    try:
        analyze_method1_screenshot()
        compare_with_reference()
        
        print(f'\n✅ 分析完成!')
        print(f'📝 请根据分析结果判断截图质量和一致性')
        
    except FileNotFoundError:
        print(f'❌ 截图文件不存在: method1_deskpilot.png')
        print('请先运行调试脚本生成截图文件')
    except Exception as e:
        print(f'❌ 分析过程中发生错误: {e}')

if __name__ == '__main__':
    main()
