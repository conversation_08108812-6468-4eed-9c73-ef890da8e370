#!/usr/bin/env python3
"""分析QuantumSeek窗口特性，尝试不同的截图方法"""

import pygetwindow as gw
import win32gui
import win32process
import win32api
from PIL import ImageGrab
import time
import os

def analyze_quantum_window():
    print('🔍 分析QuantumSeek窗口特性')
    print('=' * 50)
    
    # 1. 获取窗口信息
    print('\n📍 步骤1: 获取窗口基本信息')
    windows = gw.getWindowsWithTitle('QuantumSeek')
    
    if not windows:
        print('❌ 未找到QuantumSeek窗口')
        return
    
    window = windows[0]
    print(f'✅ 窗口标题: {window.title}')
    print(f'📍 位置: ({window.left}, {window.top})')
    print(f'📏 尺寸: {window.width} x {window.height}')
    
    # 2. 获取窗口句柄和进程信息
    print('\n🔍 步骤2: 分析窗口进程信息')
    try:
        hwnd = win32gui.FindWindow(None, window.title)
        if hwnd:
            print(f'✅ 窗口句柄: {hwnd}')
            
            # 获取进程ID
            _, pid = win32process.GetWindowThreadProcessId(hwnd)
            print(f'🔢 进程ID: {pid}')
            
            # 获取进程信息
            try:
                # 使用Windows API获取进程名称
                import win32process
                import win32con

                process_handle = win32api.OpenProcess(win32con.PROCESS_QUERY_INFORMATION | win32con.PROCESS_VM_READ, False, pid)
                if process_handle:
                    try:
                        exe_path = win32process.GetModuleFileNameEx(process_handle, 0)
                        print(f'📁 可执行文件: {exe_path}')

                        # 检查是否是Electron应用
                        exe_path_lower = exe_path.lower()
                        if 'electron' in exe_path_lower or 'chrome' in exe_path_lower:
                            print('⚡ 检测到Electron/Chrome应用')
                        elif '.exe' in exe_path_lower:
                            print(f'🔧 原生应用: {os.path.basename(exe_path)}')

                        win32api.CloseHandle(process_handle)
                    except Exception as e:
                        print(f'⚠️ 无法获取可执行文件路径: {e}')
                        win32api.CloseHandle(process_handle)
                else:
                    print('⚠️ 无法打开进程句柄')

            except Exception as e:
                print(f'⚠️ 无法获取进程信息: {e}')
        else:
            print('❌ 无法获取窗口句柄')
    except Exception as e:
        print(f'❌ 窗口分析失败: {e}')
    
    # 3. 检查窗口属性
    print('\n🏷️ 步骤3: 检查窗口属性')
    try:
        if hwnd:
            # 检查窗口样式
            style = win32gui.GetWindowLong(hwnd, win32gui.GWL_STYLE)
            ex_style = win32gui.GetWindowLong(hwnd, win32gui.GWL_EXSTYLE)
            
            print(f'🎨 窗口样式: 0x{style:08X}')
            print(f'🎨 扩展样式: 0x{ex_style:08X}')
            
            # 检查是否有分层窗口属性
            if ex_style & 0x80000:  # WS_EX_LAYERED
                print('🔍 检测到分层窗口 (WS_EX_LAYERED)')
            
            # 检查是否有透明属性
            if ex_style & 0x20:  # WS_EX_TRANSPARENT
                print('👻 检测到透明窗口 (WS_EX_TRANSPARENT)')
                
            # 检查窗口是否可见
            if win32gui.IsWindowVisible(hwnd):
                print('👁️ 窗口可见')
            else:
                print('🙈 窗口不可见')
                
            # 检查窗口是否启用
            if win32gui.IsWindowEnabled(hwnd):
                print('✅ 窗口已启用')
            else:
                print('❌ 窗口已禁用')
    except Exception as e:
        print(f'⚠️ 窗口属性检查失败: {e}')
    
    # 4. 尝试不同的截图方法
    print('\n📸 步骤4: 尝试不同的截图方法')
    
    left = window.left
    top = window.top
    right = window.left + window.width
    bottom = window.top + window.height
    
    # 方法1: PIL ImageGrab全屏后裁剪
    print('\n📷 方法1: PIL ImageGrab全屏后裁剪')
    try:
        full_screen = ImageGrab.grab()
        cropped = full_screen.crop((left, top, right, bottom))
        
        filename1 = 'method1_fullscreen_crop.png'
        cropped.save(filename1)
        
        # 检查内容
        colors = cropped.getcolors(maxcolors=256*256*256)
        if colors:
            top_color = max(colors, key=lambda x: x[0])
            if top_color[1] == (0, 0, 0) and top_color[0] > cropped.size[0] * cropped.size[1] * 0.8:
                print(f'❌ 主要是黑色 - {filename1}')
            else:
                print(f'✅ 包含内容 - {filename1}')
        
    except Exception as e:
        print(f'❌ 方法1失败: {e}')
    
    # 方法2: 激活窗口后截图
    print('\n📷 方法2: 激活窗口后截图')
    try:
        # 强制激活窗口
        if hwnd:
            win32gui.SetForegroundWindow(hwnd)
            win32gui.ShowWindow(hwnd, 9)  # SW_RESTORE
            time.sleep(2)
        
        screenshot2 = ImageGrab.grab(bbox=(left, top, right, bottom))
        filename2 = 'method2_activated_window.png'
        screenshot2.save(filename2)
        
        # 检查内容
        colors = screenshot2.getcolors(maxcolors=256*256*256)
        if colors:
            top_color = max(colors, key=lambda x: x[0])
            if top_color[1] == (0, 0, 0) and top_color[0] > screenshot2.size[0] * screenshot2.size[1] * 0.8:
                print(f'❌ 主要是黑色 - {filename2}')
            else:
                print(f'✅ 包含内容 - {filename2}')
        
    except Exception as e:
        print(f'❌ 方法2失败: {e}')
    
    # 方法3: 延迟截图
    print('\n📷 方法3: 延迟截图 (等待5秒)')
    try:
        print('⏱️ 等待5秒让窗口完全渲染...')
        time.sleep(5)
        
        screenshot3 = ImageGrab.grab(bbox=(left, top, right, bottom))
        filename3 = 'method3_delayed_screenshot.png'
        screenshot3.save(filename3)
        
        # 检查内容
        colors = screenshot3.getcolors(maxcolors=256*256*256)
        if colors:
            top_color = max(colors, key=lambda x: x[0])
            if top_color[1] == (0, 0, 0) and top_color[0] > screenshot3.size[0] * screenshot3.size[1] * 0.8:
                print(f'❌ 主要是黑色 - {filename3}')
            else:
                print(f'✅ 包含内容 - {filename3}')
        
    except Exception as e:
        print(f'❌ 方法3失败: {e}')
    
    # 5. 总结建议
    print('\n💡 步骤5: 总结和建议')
    print('=' * 50)
    print('已尝试的方法:')
    print('1. ✅ 全屏截图后裁剪')
    print('2. ✅ 激活窗口后截图')
    print('3. ✅ 延迟截图')
    print('')
    print('如果所有方法都显示黑色，可能的原因:')
    print('• QuantumSeek使用了特殊的渲染技术')
    print('• 应用有防截图保护机制')
    print('• 窗口内容需要特定的交互才能显示')
    print('• 多显示器DPI缩放问题')
    print('')
    print('建议:')
    print('1. 检查QuantumSeek是否有禁用硬件加速的选项')
    print('2. 尝试在主显示器上运行QuantumSeek')
    print('3. 检查是否有其他截图工具能成功截取QuantumSeek')
    print('4. 联系QuantumSeek开发者了解截图限制')

if __name__ == '__main__':
    analyze_quantum_window()
