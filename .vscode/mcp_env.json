{"inputs": [{"type": "promptString", "id": "debug-level", "description": "Debug Level (info/debug/error)", "default": "info"}], "servers": {"deskpilot-ts": {"type": "stdio", "command": "npx", "args": ["deskpilot-ts", "--debug"], "env": {"NODE_ENV": "development", "DEBUG": "deskpilot:*", "MCP_TIMEOUT": "30000", "DESKPILOT_LOG_LEVEL": "${input:debug-level}", "DESKPILOT_SCREENSHOT_FORMAT": "png", "DESKPILOT_SCREENSHOT_QUALITY": "90", "PATH": "${env:PATH}"}, "envFile": "${workspaceFolder}/.env"}}}