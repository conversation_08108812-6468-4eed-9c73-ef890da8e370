# DeskPilot 精准截图和UI操作指南

## 概述

DeskPilot现在支持精准的窗口截图和基于窗口相对坐标的UI操作，完美解决了多显示器环境下的桌面自动化问题。

## 🎯 核心功能

### 1. 精准窗口截图

#### 特性
- ✅ **多显示器支持**: 使用Windows API真正截取副显示器窗口内容
- ✅ **精确区域截图**: 只截取目标窗口区域，不包含其他内容
- ✅ **尺寸匹配验证**: 确保截图尺寸与窗口尺寸完全匹配
- ✅ **智能回退机制**: 多种截图方法确保成功率

#### 使用方法
```python
from desk_pilot.core import DeskPilotCore

core = DeskPilotCore(visual_mode=True)

# 精准截图（默认启用）
result = core.capture_window('QuantumSeek', precise_region=True)

if result['success']:
    # 截图尺寸与窗口尺寸完全匹配
    window_info = result['window_info']
    print(f"窗口尺寸: {window_info['width']} x {window_info['height']}")
    
    # 保存截图
    import base64
    with open('precise_screenshot.png', 'wb') as f:
        f.write(base64.b64decode(result['screenshot_base64']))
```

### 2. 窗口相对坐标系统

#### 特性
- ✅ **相对坐标转换**: 窗口内坐标自动转换为屏幕绝对坐标
- ✅ **坐标验证**: 确保操作坐标在窗口范围内
- ✅ **多显示器兼容**: 正确处理副显示器窗口坐标
- ✅ **双向转换**: 支持相对↔绝对坐标互转

#### 坐标转换API
```python
# 获取窗口信息
window_info = core.get_window_info('QuantumSeek')

# 相对坐标转绝对坐标
relative_x, relative_y = 100, 50  # 窗口内坐标
absolute_x, absolute_y = core.convert_relative_to_absolute(
    'QuantumSeek', relative_x, relative_y
)

# 绝对坐标转相对坐标
back_relative_x, back_relative_y = core.convert_absolute_to_relative(
    'QuantumSeek', absolute_x, absolute_y
)
```

### 3. 精准UI操作

#### 支持的操作类型
- **点击操作**: 支持相对/绝对坐标点击
- **滚动操作**: 支持相对/绝对坐标滚动
- **文本输入**: 智能输入法切换
- **按键操作**: 支持组合键

#### 相对坐标操作示例
```python
# 使用相对坐标点击窗口中心
result = core.perform_ui_action(
    action_type="click",
    window_title="QuantumSeek",
    x=window_info["width"] // 2,   # 窗口中心X
    y=window_info["height"] // 2,  # 窗口中心Y
    relative_coords=True           # 启用相对坐标
)

# 使用相对坐标滚动
result = core.perform_ui_action(
    action_type="scroll",
    window_title="QuantumSeek",
    x=200, y=300,                  # 窗口内坐标
    clicks=3,
    relative_coords=True
)
```

#### 绝对坐标操作示例
```python
# 使用绝对坐标点击（传统方式）
result = core.perform_ui_action(
    action_type="click",
    window_title="QuantumSeek",
    x=7196, y=792,                 # 屏幕绝对坐标
    relative_coords=False          # 使用绝对坐标
)
```

## 🔧 技术实现

### 多显示器截图技术
1. **Windows API集成**: 使用win32gui、win32ui进行真正的多显示器截图
2. **智能检测**: 自动检测窗口是否在副显示器上
3. **资源管理**: 正确清理Windows API资源避免内存泄漏

### 坐标转换算法
```python
# 相对坐标 -> 绝对坐标
absolute_x = window_left + relative_x
absolute_y = window_top + relative_y

# 绝对坐标 -> 相对坐标  
relative_x = absolute_x - window_left
relative_y = absolute_y - window_top
```

### 窗口信息刷新机制
- 自动检测并处理最小化窗口（-48000位置）
- 多次重试确保获取最新窗口位置
- 智能恢复最小化窗口

## 📊 测试验证

### 测试覆盖
- ✅ 窗口信息获取准确性
- ✅ 坐标转换双向一致性  
- ✅ 精准截图尺寸匹配
- ✅ UI操作坐标验证
- ✅ 多显示器兼容性

### 运行测试
```bash
# 精准截图和坐标转换测试
python test_precise_capture.py

# UI操作演示
python demo_precise_ui_operations.py

# 多显示器截图测试
python final_multimonitor_test.py
```

## 🎨 可视化模式

### 特性
- 🖱️ **鼠标移动可视化**: 先移动到目标位置再执行操作
- ⏱️ **操作延迟**: 可配置的操作间隔时间
- 📝 **详细日志**: 实时显示操作过程和坐标转换
- 🎯 **操作确认**: 清晰显示每个操作的执行结果

### 启用方式
```python
# 启用可视化模式，延迟1秒
core = DeskPilotCore(visual_mode=True, operation_delay=1.0)

# 禁用可视化模式（快速执行）
core = DeskPilotCore(visual_mode=False)
```

## 🚀 最佳实践

### 1. 窗口操作建议
- 使用相对坐标进行窗口内元素操作
- 使用绝对坐标进行跨窗口操作
- 操作前先获取最新窗口信息

### 2. 多显示器环境
- DeskPilot自动检测并处理多显示器
- 无需手动配置，开箱即用
- 支持任意分辨率和排列方式

### 3. 调试和开发
- 启用可视化模式观察操作过程
- 使用日志查看坐标转换详情
- 保存截图验证捕获内容

### 4. 性能优化
- 禁用可视化模式提高执行速度
- 合理设置操作延迟避免过快操作
- 复用窗口信息减少重复获取

## 📋 API参考

### 核心方法
- `capture_window(window_title, precise_region=True)`: 精准窗口截图
- `get_window_info(window_title, refresh=True)`: 获取窗口详细信息
- `convert_relative_to_absolute(window_title, x, y)`: 相对坐标转绝对坐标
- `convert_absolute_to_relative(window_title, x, y)`: 绝对坐标转相对坐标
- `perform_ui_action(action_type, window_title, **params)`: 执行UI操作

### 操作参数
- `relative_coords`: 是否使用相对坐标（默认False）
- `window_title`: 目标窗口标题
- `visual_mode`: 是否启用可视化模式
- `operation_delay`: 操作延迟时间（秒）

## 🎉 总结

DeskPilot现在提供了完整的精准桌面自动化解决方案：

1. **精准截图**: 真正的窗口区域截图，支持多显示器
2. **智能坐标**: 相对坐标系统简化窗口内操作
3. **可视化调试**: 清晰的操作过程展示
4. **稳定可靠**: 多重回退机制确保成功率

这些功能让DeskPilot成为Windows桌面自动化的强大工具，特别适合多显示器环境下的精确操作需求。
