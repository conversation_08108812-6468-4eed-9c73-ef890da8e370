# DeskPilot 设计方案

## 概述

DeskPilot 是一个基于 Model Context Protocol (MCP) 的工具，旨在辅助 AI 模型实现 Windows 桌面工具开发和交互。它允许 AI 模型直接访问桌面工具的开发效果并实现交互，特别是针对 Tauri 应用程序的开发和测试。

## 技术架构

### 整体架构

DeskPilot 由以下几个核心层组成：

1. **MCP 服务器层** - 实现 MCP 协议，提供工具接口
2. **桌面交互层** - 截图、UI 操作、窗口管理
3. **Tauri 集成层** - 与 Tauri 应用交互
4. **客户端通信层** - 与 AI 客户端通信

### 技术栈

- **Python 3.12+**: 作为主要开发语言
- **MCP SDK**: 实现 Model Context Protocol 服务器
- **PyAutoGUI/PyGetWindow**: 用于屏幕截图和窗口操作
- **Tauri API**: 与 Tauri 应用程序交互
- **FastAPI**: 提供 HTTP API (可选)

## 核心功能模块

### DeskPilotCore

核心功能实现，包括：

- 窗口截图捕获
- UI 操作执行（点击、输入文本、按键等）
- 窗口管理

```python
class DeskPilotCore:
    """DeskPilot 核心功能实现"""
    
    def capture_window(self, window_title: Optional[str] = None) -> Dict[str, Any]:
        """捕获指定窗口或活动窗口的截图"""
        # 实现窗口截图功能
        pass
    
    def perform_ui_action(self, action_type: str, **params) -> Dict[str, Any]:
        """执行 UI 操作，如点击、输入文本等"""
        # 实现 UI 操作功能
        pass
```

### MCP 服务器实现

实现 MCP 协议服务器，提供工具接口：

```python
class DeskPilotMcpServer:
    """DeskPilot MCP 服务器实现"""
    
    def __init__(self):
        self.core = DeskPilotCore()
        self.server = None
    
    def start(self, transport_provider=None):
        """启动 MCP 服务器"""
        # 创建并启动 MCP 服务器
        pass
    
    def _register_tools(self):
        """注册 MCP 工具"""
        # 注册截图工具、UI 操作工具等
        pass
```

### Tauri 集成模块

与 Tauri 应用程序交互的模块：

```python
class TauriIntegration:
    """Tauri 应用集成"""
    
    def create_project(self, name: str, template: str = "vanilla") -> Dict[str, Any]:
        """创建新的 Tauri 项目"""
        pass
    
    def build_project(self) -> Dict[str, Any]:
        """构建 Tauri 项目"""
        pass
    
    def dev_preview(self) -> Dict[str, Any]:
        """启动开发预览"""
        pass
```

## MCP 工具接口

DeskPilot 提供以下 MCP 工具接口：

1. **capture-window**: 捕获窗口截图
   - 参数：`window_title` (可选) - 要截图的窗口标题
   - 返回：包含截图 base64 编码、窗口标题和尺寸的 JSON 对象

2. **ui-action**: 执行 UI 操作
   - 参数：
     - `action_type`: 操作类型 ("click", "type", "key")
     - `x`, `y`: 点击坐标 (click 操作)
     - `text`: 要输入的文本 (type 操作)
     - `key`: 要按下的键 (key 操作)
   - 返回：操作结果 JSON 对象

3. **tauri-create**: 创建 Tauri 项目
   - 参数：
     - `name`: 项目名称
     - `template`: 模板类型 (默认 "vanilla")
   - 返回：创建结果 JSON 对象

4. **tauri-dev**: 启动 Tauri 开发预览
   - 参数：`project_dir` (可选) - 项目目录
   - 返回：启动结果 JSON 对象

## 使用方法

### 安装

```bash
pip install desk-pilot
```

### 启动 MCP 服务器

```bash
desk-pilot
```

### 在 AI 客户端中配置

在支持 MCP 的 AI 客户端（如 Claude）中配置：

```json
{
  "mcpServers": {
    "deskPilot": {
      "command": "desk-pilot",
      "args": ["--debug"]
    }
  }
}
```

### 使用示例

AI 模型可以通过 MCP 工具接口执行以下操作：

1. 捕获 Tauri 应用窗口截图
2. 分析 UI 并执行交互操作
3. 创建和修改 Tauri 项目
4. 启动开发预览并实时查看效果

## 扩展功能

1. **UI 元素识别**: 添加 OCR 和 UI 元素识别功能
2. **智能交互**: 基于 UI 理解的智能交互
3. **项目模板管理**: 管理和应用多种 Tauri 项目模板
4. **多窗口比较**: 支持多窗口管理和比较
5. **代码生成**: 根据 UI 交互自动生成代码

## 技术依赖

- Python 3.12+
- MCP SDK
- PyAutoGUI
- PyGetWindow
- Pillow
- Rich (用于日志和控制台输出)
- Tauri CLI (用于 Tauri 项目管理)

## 项目结构

```
desk-pilot/
├── desk_pilot/
│   ├── __init__.py
│   ├── __main__.py
│   ├── core.py
│   ├── mcp_server.py
│   └── tauri_integration.py
├── docs/
│   └── desk_pilot_design.md
├── tests/
│   └── ...
└── pyproject.toml
```