#!/usr/bin/env python3
"""调试窗口坐标获取"""

import pygetwindow as gw
from desk_pilot.core import DeskPilotCore

def debug_coordinates():
    print('🔍 调试窗口坐标获取...')
    
    # 直接使用pygetwindow获取坐标
    print('\n📍 直接使用pygetwindow:')
    windows = gw.getWindowsWithTitle('QuantumSeek')
    if windows:
        window = windows[0]
        print(f'   窗口: {window.title}')
        print(f'   位置: ({window.left}, {window.top})')
        print(f'   尺寸: {window.width} x {window.height}')
    else:
        print('   未找到QuantumSeek窗口')
    
    # 使用DeskPilot获取坐标
    print('\n📍 使用DeskPilot capture_window:')
    core = DeskPilotCore(visual_mode=False)  # 关闭可视化模式以减少干扰
    
    # 添加调试日志
    import logging
    logging.basicConfig(level=logging.DEBUG)
    
    result = core.capture_window('QuantumSeek')
    if result.get('success'):
        window_info = result['window_info']
        print(f'   窗口: {result["window_title"]}')
        print(f'   位置: ({window_info["left"]}, {window_info["top"]})')
        print(f'   尺寸: {window_info["width"]} x {window_info["height"]}')

        # 分析截图数据
        import base64
        from PIL import Image
        import io

        image_data = base64.b64decode(result['screenshot_base64'])
        img = Image.open(io.BytesIO(image_data))
        print(f'   截图尺寸: {img.size}')

        # 检查是否成功截取了窗口区域
        if img.size == (window_info["width"], window_info["height"]):
            print('   ✅ 截图尺寸匹配窗口尺寸 - Windows API成功！')
        else:
            print('   ⚠️ 截图尺寸不匹配 - 可能使用了回退方法')
    else:
        print(f'   失败: {result.get("error")}')

if __name__ == '__main__':
    debug_coordinates()
