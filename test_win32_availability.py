#!/usr/bin/env python3
"""测试Windows API可用性"""

try:
    import win32gui
    import win32ui
    import win32con
    import win32api
    HAS_WIN32 = True
    print('✅ Windows API可用')
    print(f'   win32gui: {win32gui}')
    print(f'   win32ui: {win32ui}')
    print(f'   win32con: {win32con}')
    print(f'   win32api: {win32api}')
except ImportError as e:
    HAS_WIN32 = False
    print(f'❌ Windows API不可用: {e}')

print(f'HAS_WIN32 = {HAS_WIN32}')

# 测试简单的Windows API调用
if HAS_WIN32:
    try:
        hdesktop = win32gui.GetDesktopWindow()
        print(f'✅ GetDesktopWindow成功: {hdesktop}')
        
        desktop_dc = win32gui.GetWindowDC(hdesktop)
        print(f'✅ GetWindowDC成功: {desktop_dc}')
        
        # 清理
        win32gui.ReleaseDC(hdesktop, desktop_dc)
        print('✅ Windows API基本调用成功')
        
    except Exception as e:
        print(f'❌ Windows API调用失败: {e}')
