#!/usr/bin/env python3
"""
测试WebView2自动化功能

这个脚本用于测试新的WebView2自动化功能，验证是否能够正确检测和连接WebView2应用。
"""

import asyncio
import logging
from desk_pilot.webview2_automation import WebView2AutomationManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_webview2_detection():
    """测试WebView2应用检测功能"""
    print("🔍 测试WebView2应用检测功能...")
    
    try:
        manager = WebView2AutomationManager(visual_mode=True)
        
        # 检测WebView2应用
        result = manager.detect_webview2_apps()
        
        if result.get("success"):
            apps = result.get("apps", [])
            print(f"✅ 检测成功！找到 {len(apps)} 个潜在的WebView2应用:")
            
            for i, app in enumerate(apps, 1):
                debug_status = "✅ 有调试端口" if app.get("has_debug_port") else "❌ 无调试端口"
                print(f"  {i}. PID: {app['pid']}")
                print(f"     名称: {app['name']}")
                print(f"     调试端口: {app.get('debug_port', 'N/A')}")
                print(f"     状态: {debug_status}")
                print(f"     命令行: {app.get('cmdline', 'N/A')[:100]}...")
                print()
            
            return apps
        else:
            print(f"❌ 检测失败: {result.get('error')}")
            return []
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return []

async def test_webview2_connection(apps):
    """测试WebView2连接功能"""
    print("🔗 测试WebView2连接功能...")
    
    if not apps:
        print("⚠️ 没有可用的WebView2应用进行连接测试")
        return None
    
    # 查找有调试端口的应用
    apps_with_debug = [app for app in apps if app.get("has_debug_port")]
    
    if not apps_with_debug:
        print("⚠️ 没有找到启用调试端口的WebView2应用")
        print("💡 提示: 启动WebView2应用时需要添加 --remote-debugging-port=9222 参数")
        return None
    
    try:
        manager = WebView2AutomationManager(visual_mode=True)
        
        # 尝试连接第一个有调试端口的应用
        app = apps_with_debug[0]
        debug_port = app.get("debug_port")
        
        print(f"🎯 尝试连接到应用: {app['name']} (端口: {debug_port})")
        
        result = manager.connect_to_webview2(debug_port=debug_port)
        
        if result.get("success"):
            print("✅ 连接成功!")
            print(f"   应用: {result.get('connected_app')}")
            print(f"   调试端口: {result.get('debug_port')}")
            print(f"   会话ID: {result.get('driver_session_id')}")
            
            # 测试基本功能
            await test_basic_operations(manager)
            
            # 断开连接
            disconnect_result = manager.disconnect()
            print(f"🔌 断开连接: {disconnect_result.get('message')}")
            
            return manager
        else:
            print(f"❌ 连接失败: {result.get('error')}")
            return None
            
    except Exception as e:
        print(f"❌ 连接测试过程中出现错误: {e}")
        return None

async def test_basic_operations(manager):
    """测试基本WebView2操作"""
    print("\n🧪 测试基本WebView2操作...")
    
    try:
        # 测试获取连接状态
        status_result = manager.get_connection_status()
        if status_result.get("success") and status_result.get("connected"):
            print("✅ 连接状态检查通过")
            print(f"   当前URL: {status_result.get('current_url', 'N/A')}")
            print(f"   页面标题: {status_result.get('page_title', 'N/A')}")
        
        # 测试截图功能
        print("📸 测试截图功能...")
        screenshot_result = manager.capture_screenshot()
        if screenshot_result.get("success"):
            print("✅ 截图成功!")
            print(f"   页面标题: {screenshot_result.get('page_title', 'N/A')}")
            print(f"   截图格式: {screenshot_result.get('screenshot_format', 'N/A')}")
            screenshot_data = screenshot_result.get('screenshot_base64', '')
            print(f"   截图大小: {len(screenshot_data)} 字符")
        else:
            print(f"❌ 截图失败: {screenshot_result.get('error')}")
        
        # 测试JavaScript执行
        print("🔧 测试JavaScript执行...")
        script_result = manager.execute_script("return document.title;")
        if script_result.get("success"):
            print(f"✅ JavaScript执行成功! 页面标题: {script_result.get('result')}")
        else:
            print(f"❌ JavaScript执行失败: {script_result.get('error')}")
        
    except Exception as e:
        print(f"❌ 基本操作测试过程中出现错误: {e}")

async def main():
    """主测试函数"""
    print("🚀 开始WebView2自动化功能测试")
    print("=" * 50)
    
    # 1. 测试应用检测
    apps = await test_webview2_detection()
    
    print("\n" + "=" * 50)
    
    # 2. 测试连接功能
    if apps:
        await test_webview2_connection(apps)
    else:
        print("⚠️ 跳过连接测试，因为没有检测到WebView2应用")
    
    print("\n" + "=" * 50)
    print("🏁 WebView2自动化功能测试完成")
    
    if not apps:
        print("\n💡 使用建议:")
        print("1. 确保有WebView2应用正在运行")
        print("2. 启动应用时添加调试参数，例如:")
        print("   your-app.exe --remote-debugging-port=9222")
        print("3. 或者使用支持WebView2的应用，如基于Electron或Tauri的应用")

if __name__ == "__main__":
    asyncio.run(main())
