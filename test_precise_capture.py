#!/usr/bin/env python3
"""测试精准截图和坐标转换功能"""

import logging
from desk_pilot.core import DeskPilotCore
import pygetwindow as gw
import base64
from PIL import Image
import io

# 设置详细日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')

def test_precise_capture():
    print('🎯 测试精准截图和坐标转换功能')
    print('=' * 60)
    
    # 1. 获取QuantumSeek窗口信息
    print('\n📍 步骤1: 获取窗口信息')
    windows = gw.getWindowsWithTitle('QuantumSeek')
    if not windows:
        print('❌ 未找到QuantumSeek窗口')
        return
    
    window = windows[0]
    print(f'✅ 找到窗口: {window.title}')
    print(f'📍 位置: ({window.left}, {window.top})')
    print(f'📏 尺寸: {window.width} x {window.height}')
    
    # 2. 创建DeskPilot实例并测试窗口信息获取
    print('\n🔧 步骤2: 测试DeskPilot窗口信息获取')
    core = DeskPilotCore(visual_mode=True)
    
    window_info = core.get_window_info('QuantumSeek')
    if window_info:
        print('✅ DeskPilot窗口信息获取成功:')
        print(f'   位置: ({window_info["left"]}, {window_info["top"]})')
        print(f'   尺寸: {window_info["width"]} x {window_info["height"]}')
        print(f'   中心点: ({window_info["center_x"]}, {window_info["center_y"]})')
        print(f'   副显示器: {window_info["is_on_secondary_monitor"]}')
    else:
        print('❌ DeskPilot窗口信息获取失败')
        return
    
    # 3. 测试坐标转换功能
    print('\n🔄 步骤3: 测试坐标转换功能')
    
    # 测试相对坐标转换为绝对坐标
    relative_x, relative_y = 100, 50  # 窗口内相对坐标
    absolute_x, absolute_y = core.convert_relative_to_absolute('QuantumSeek', relative_x, relative_y)
    print(f'相对坐标 ({relative_x}, {relative_y}) -> 绝对坐标 ({absolute_x}, {absolute_y})')
    
    # 测试绝对坐标转换为相对坐标
    back_relative_x, back_relative_y = core.convert_absolute_to_relative('QuantumSeek', absolute_x, absolute_y)
    print(f'绝对坐标 ({absolute_x}, {absolute_y}) -> 相对坐标 ({back_relative_x}, {back_relative_y})')
    
    if relative_x == back_relative_x and relative_y == back_relative_y:
        print('✅ 坐标转换测试通过')
    else:
        print('❌ 坐标转换测试失败')
    
    # 4. 测试精准截图功能
    print('\n📸 步骤4: 测试精准截图功能')
    
    # 测试精准模式截图
    print('🎯 测试精准模式截图...')
    result_precise = core.capture_window('QuantumSeek', precise_region=True)
    
    if result_precise.get('success'):
        print('✅ 精准截图成功')
        
        # 解码并分析截图
        image_data = base64.b64decode(result_precise['screenshot_base64'])
        img = Image.open(io.BytesIO(image_data))
        
        print(f'📸 截图尺寸: {img.size}')
        print(f'📏 窗口尺寸: ({window_info["width"]}, {window_info["height"]})')
        
        # 保存精准截图
        with open('precise_capture_test.png', 'wb') as f:
            f.write(image_data)
        print('💾 精准截图保存为: precise_capture_test.png')
        
        # 验证尺寸匹配
        if img.size == (window_info["width"], window_info["height"]):
            print('✅ 截图尺寸完全匹配窗口尺寸 - 精准截图成功！')
        else:
            print(f'⚠️ 截图尺寸不匹配: 期望 {(window_info["width"], window_info["height"])}, 实际 {img.size}')
        
        # 分析截图内容
        colors = img.getcolors(maxcolors=256*256*256)
        if colors:
            top_colors = sorted(colors, key=lambda x: x[0], reverse=True)[:3]
            print('\n🎨 截图主要颜色:')
            for i, (count, color) in enumerate(top_colors):
                percentage = (count / (img.size[0] * img.size[1])) * 100
                print(f'   {i+1}. RGB{color} - {percentage:.1f}%')
            
            # 检查是否主要是黑色
            if top_colors[0][1] == (0, 0, 0) and top_colors[0][0] > img.size[0] * img.size[1] * 0.8:
                print('⚠️ 截图主要是黑色，可能窗口被遮挡')
            else:
                print('✅ 截图包含有效内容')
    else:
        print(f'❌ 精准截图失败: {result_precise.get("error")}')
    
    # 5. 测试UI操作坐标转换
    print('\n🖱️ 步骤5: 测试UI操作坐标转换')
    
    # 测试相对坐标点击（不实际执行，只验证坐标转换）
    print('🎯 测试相对坐标点击转换...')
    
    # 模拟在窗口中心点击
    center_relative_x = window_info["width"] // 2
    center_relative_y = window_info["height"] // 2
    
    print(f'窗口中心相对坐标: ({center_relative_x}, {center_relative_y})')
    
    # 转换为绝对坐标
    center_absolute_x, center_absolute_y = core.convert_relative_to_absolute(
        'QuantumSeek', center_relative_x, center_relative_y
    )
    print(f'窗口中心绝对坐标: ({center_absolute_x}, {center_absolute_y})')
    
    # 验证是否在窗口范围内
    if (window_info["left"] <= center_absolute_x <= window_info["right"] and 
        window_info["top"] <= center_absolute_y <= window_info["bottom"]):
        print('✅ 坐标转换正确，点击位置在窗口范围内')
    else:
        print('❌ 坐标转换错误，点击位置超出窗口范围')
    
    # 6. 总结测试结果
    print('\n📋 步骤6: 测试总结')
    print('=' * 60)
    
    success_count = 0
    total_tests = 4
    
    if window_info:
        success_count += 1
        print('✅ 窗口信息获取: 成功')
    else:
        print('❌ 窗口信息获取: 失败')
    
    if relative_x == back_relative_x and relative_y == back_relative_y:
        success_count += 1
        print('✅ 坐标转换: 成功')
    else:
        print('❌ 坐标转换: 失败')
    
    if result_precise.get('success'):
        success_count += 1
        print('✅ 精准截图: 成功')
    else:
        print('❌ 精准截图: 失败')
    
    if (window_info["left"] <= center_absolute_x <= window_info["right"] and 
        window_info["top"] <= center_absolute_y <= window_info["bottom"]):
        success_count += 1
        print('✅ UI操作坐标: 成功')
    else:
        print('❌ UI操作坐标: 失败')
    
    print(f'\n🎯 测试完成: {success_count}/{total_tests} 项测试通过')
    
    if success_count == total_tests:
        print('🎉 所有测试通过！精准截图和坐标转换功能正常工作')
    else:
        print('⚠️ 部分测试失败，需要进一步调试')

if __name__ == '__main__':
    test_precise_capture()
