#!/usr/bin/env python3
"""使用PIL ImageGrab直接截取窗口测试"""

import pygetwindow as gw
from PIL import ImageGrab
import time

def test_pil_direct_capture():
    print('🎯 使用PIL ImageGrab直接截取QuantumSeek窗口')
    print('=' * 50)
    
    # 1. 获取窗口信息
    print('\n📍 步骤1: 获取窗口信息')
    windows = gw.getWindowsWithTitle('QuantumSeek')
    
    if not windows:
        print('❌ 未找到QuantumSeek窗口')
        return
    
    window = windows[0]
    print(f'✅ 找到窗口: {window.title}')
    print(f'📍 位置: ({window.left}, {window.top})')
    print(f'📏 尺寸: {window.width} x {window.height}')
    print(f'🔄 状态: 激活={window.isActive}, 最小化={window.isMinimized}')
    
    # 2. 激活窗口
    print('\n🔄 步骤2: 激活窗口')
    try:
        if window.isMinimized:
            window.restore()
            time.sleep(1)
        
        window.activate()
        time.sleep(2)
        print('✅ 窗口已激活')
    except Exception as e:
        print(f'⚠️ 激活窗口出错: {e}')
    
    # 3. 使用PIL ImageGrab截图
    print('\n📸 步骤3: 使用PIL ImageGrab截图')
    
    # 计算截图区域
    left = window.left
    top = window.top
    right = window.left + window.width
    bottom = window.top + window.height
    
    print(f'📏 截图区域: ({left}, {top}, {right}, {bottom})')
    
    try:
        # 使用PIL ImageGrab截取指定区域
        screenshot = ImageGrab.grab(bbox=(left, top, right, bottom))
        print(f'✅ PIL截图成功，尺寸: {screenshot.size}')
        
        # 保存截图
        filename = 'pil_quantum_screenshot.png'
        screenshot.save(filename)
        print(f'💾 截图已保存为: {filename}')
        
        # 分析颜色
        colors = screenshot.getcolors(maxcolors=256*256*256)
        if colors:
            top_colors = sorted(colors, key=lambda x: x[0], reverse=True)[:10]
            total_pixels = screenshot.size[0] * screenshot.size[1]
            
            print('\n🎨 主要颜色分析:')
            for i, (count, color) in enumerate(top_colors):
                percentage = (count / total_pixels) * 100
                print(f'   {i+1:2d}. RGB{color} - {percentage:5.1f}%')
            
            # 检查是否主要是黑色
            if top_colors[0][1] == (0, 0, 0) and top_colors[0][0] > total_pixels * 0.8:
                print('⚠️ PIL截图也主要是黑色')
            else:
                print('✅ PIL截图包含有效内容')
                
                # 检查QuantumSeek特征颜色
                purple_found = False
                for count, (r, g, b) in top_colors:
                    if 40 <= r <= 120 and 30 <= g <= 90 and 60 <= b <= 150:
                        purple_found = True
                        print(f'✅ 检测到紫色系: RGB({r}, {g}, {b})')
                        break
                
                if not purple_found:
                    print('⚠️ 未检测到明显的紫色主题色')
        
    except Exception as e:
        print(f'❌ PIL截图失败: {e}')
    
    # 4. 尝试全屏截图作为对比
    print('\n📸 步骤4: 全屏截图对比')
    try:
        full_screenshot = ImageGrab.grab()
        print(f'✅ 全屏截图成功，尺寸: {full_screenshot.size}')
        
        # 从全屏截图中裁剪窗口区域
        cropped = full_screenshot.crop((left, top, right, bottom))
        
        filename_cropped = 'fullscreen_cropped_quantum.png'
        cropped.save(filename_cropped)
        print(f'💾 裁剪后截图保存为: {filename_cropped}')
        
        # 分析裁剪后的颜色
        colors_cropped = cropped.getcolors(maxcolors=256*256*256)
        if colors_cropped:
            top_colors_cropped = sorted(colors_cropped, key=lambda x: x[0], reverse=True)[:5]
            total_pixels_cropped = cropped.size[0] * cropped.size[1]
            
            print('\n🎨 裁剪截图主要颜色:')
            for i, (count, color) in enumerate(top_colors_cropped):
                percentage = (count / total_pixels_cropped) * 100
                print(f'   {i+1}. RGB{color} - {percentage:5.1f}%')
            
            if not (top_colors_cropped[0][1] == (0, 0, 0) and top_colors_cropped[0][0] > total_pixels_cropped * 0.8):
                print('✅ 裁剪截图包含有效内容')
            else:
                print('⚠️ 裁剪截图仍然主要是黑色')
        
    except Exception as e:
        print(f'❌ 全屏截图失败: {e}')
    
    # 5. 总结
    print('\n📋 总结')
    print('=' * 50)
    print('测试了多种截图方法:')
    print('1. DeskPilot Windows API截图')
    print('2. PIL ImageGrab直接区域截图') 
    print('3. 全屏截图后裁剪')
    print('')
    print('如果所有方法都显示黑色，可能的原因:')
    print('• QuantumSeek窗口被其他窗口完全遮挡')
    print('• 窗口内容渲染问题')
    print('• 窗口处于特殊显示模式')
    print('• 多显示器坐标计算问题')

if __name__ == '__main__':
    test_pil_direct_capture()
