#!/usr/bin/env python3
"""测试PrintWindow方法修复硬件加速黑屏问题"""

import logging
from desk_pilot.core import DeskPilotCore
import base64
from PIL import Image
import io
import time

# 设置详细日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')

def test_printwindow_fix():
    print('🎯 测试PrintWindow方法修复硬件加速黑屏问题')
    print('=' * 60)
    
    # 创建DeskPilot实例
    core = DeskPilotCore(visual_mode=True, operation_delay=0.5)
    
    # 1. 获取QuantumSeek窗口信息
    print('\n📍 步骤1: 获取QuantumSeek窗口信息')
    window_info = core.get_window_info('QuantumSeek')
    
    if not window_info:
        print('❌ 未找到QuantumSeek窗口')
        return
    
    print(f'✅ 窗口信息:')
    print(f'   标题: {window_info["title"]}')
    print(f'   位置: ({window_info["left"]}, {window_info["top"]})')
    print(f'   尺寸: {window_info["width"]} x {window_info["height"]}')
    print(f'   副显示器: {window_info["is_on_secondary_monitor"]}')
    print(f'   激活状态: {window_info["is_active"]}')
    
    # 2. 使用新的PrintWindow方法截图
    print('\n📸 步骤2: 使用PrintWindow方法截图')
    print('正在使用改进的Windows API方法截取QuantumSeek窗口...')
    
    result = core.capture_window('QuantumSeek', precise_region=True)
    
    if not result.get('success'):
        print(f'❌ 截图失败: {result.get("error")}')
        return
    
    print('✅ 截图成功！')
    
    # 3. 分析截图内容
    print('\n🔍 步骤3: 分析截图内容')
    
    # 解码截图
    image_data = base64.b64decode(result['screenshot_base64'])
    img = Image.open(io.BytesIO(image_data))
    
    print(f'📏 截图尺寸: {img.size}')
    print(f'📏 窗口尺寸: ({window_info["width"]}, {window_info["height"]})')
    
    # 验证尺寸匹配
    if img.size == (window_info["width"], window_info["height"]):
        print('✅ 截图尺寸与窗口尺寸完全匹配')
    else:
        print(f'⚠️ 截图尺寸不匹配')
    
    # 保存截图
    filename = 'printwindow_quantum_screenshot.png'
    with open(filename, 'wb') as f:
        f.write(image_data)
    print(f'💾 截图已保存为: {filename}')
    
    # 4. 详细颜色分析
    print('\n🎨 步骤4: 详细颜色分析')
    
    colors = img.getcolors(maxcolors=256*256*256)
    if colors:
        # 获取主要颜色
        top_colors = sorted(colors, key=lambda x: x[0], reverse=True)[:15]
        total_pixels = img.size[0] * img.size[1]
        
        print('主要颜色分布:')
        for i, (count, color) in enumerate(top_colors):
            percentage = (count / total_pixels) * 100
            print(f'   {i+1:2d}. RGB{color} - {percentage:5.1f}% ({count:,} 像素)')
        
        # 检查是否还是黑屏
        is_mostly_black = (top_colors[0][1] == (0, 0, 0) and 
                          top_colors[0][0] > total_pixels * 0.8)
        
        if is_mostly_black:
            print('\n⚠️ 截图仍然主要是黑色')
            print('💡 可能的原因:')
            print('   • 窗口仍然被遮挡')
            print('   • QuantumSeek使用了更特殊的渲染方式')
            print('   • 需要窗口处于特定状态')
        else:
            print('\n🎉 截图包含有效内容！PrintWindow方法成功！')
            
            # 检查QuantumSeek特征颜色
            features_detected = []
            
            # 检查紫色主题
            purple_colors = []
            for count, (r, g, b) in top_colors:
                if 40 <= r <= 150 and 30 <= g <= 100 and 60 <= b <= 180:
                    purple_colors.append((count, (r, g, b)))
            
            if purple_colors:
                features_detected.append("紫色主题")
                print(f'\n🎯 检测到紫色主题色:')
                for count, color in purple_colors[:3]:
                    percentage = (count / total_pixels) * 100
                    print(f'   RGB{color} - {percentage:.1f}%')
            
            # 检查深色背景
            dark_colors = []
            for count, (r, g, b) in top_colors:
                if r < 60 and g < 60 and b < 60 and (r, g, b) != (0, 0, 0):
                    dark_colors.append((count, (r, g, b)))
            
            if dark_colors:
                features_detected.append("深色背景")
                print(f'\n🌑 检测到深色背景:')
                for count, color in dark_colors[:3]:
                    percentage = (count / total_pixels) * 100
                    print(f'   RGB{color} - {percentage:.1f}%')
            
            # 检查颜色多样性
            if len(top_colors) >= 8:
                features_detected.append("丰富的颜色层次")
                print(f'\n🌈 检测到丰富的颜色层次 ({len(top_colors)} 种主要颜色)')
            
            # 检查是否有白色或浅色文本
            light_colors = []
            for count, (r, g, b) in top_colors:
                if r > 200 and g > 200 and b > 200:
                    light_colors.append((count, (r, g, b)))
            
            if light_colors:
                features_detected.append("浅色文本")
                print(f'\n💡 检测到浅色文本:')
                for count, color in light_colors[:2]:
                    percentage = (count / total_pixels) * 100
                    print(f'   RGB{color} - {percentage:.1f}%')
    
    # 5. 与用户截图对比
    print('\n📊 步骤5: 与用户截图对比')
    print('用户提供的手动截图特征:')
    print('   • 紫色主题界面 (QuantumSeek品牌色)')
    print('   • 左侧导航栏 (搜索、历史、导入、设置)')
    print('   • 中央搜索区域 (搜索框和文件类型选择)')
    print('   • 底部统计信息 (0个搜索结果、0s搜索时间等)')
    print('   • 右上角功能按钮')
    print('   • 整体深色主题配色')
    
    if not is_mostly_black:
        print('\n🎯 特征匹配分析:')
        if features_detected:
            for feature in features_detected:
                print(f'   ✅ {feature}')
            
            match_score = len(features_detected)
            if match_score >= 3:
                print(f'\n🎉 高度匹配！检测到 {match_score} 个QuantumSeek特征')
                print('✅ PrintWindow方法成功解决了硬件加速黑屏问题！')
                print('🔍 建议用户查看截图文件确认内容一致性')
            elif match_score >= 2:
                print(f'\n✅ 部分匹配！检测到 {match_score} 个QuantumSeek特征')
                print('🔍 建议用户查看截图文件进行详细对比')
            else:
                print(f'\n⚠️ 低匹配度，仅检测到 {match_score} 个特征')
        else:
            print('   ⚠️ 未检测到明显的QuantumSeek特征')
    
    # 6. 总结
    print('\n📋 总结')
    print('=' * 60)
    print(f'📸 截图文件: {filename}')
    print(f'📏 截图尺寸: {img.size}')
    
    if not is_mostly_black:
        print('🎉 PrintWindow方法成功获取到有效内容！')
        print('✅ 硬件加速黑屏问题已解决')
        print('')
        print('🔧 技术细节:')
        print('   • 使用PrintWindow API替代BitBlt')
        print('   • 添加PW_RENDERFULLCONTENT参数')
        print('   • 直接从窗口句柄获取内容')
        print('   • 绕过硬件加速渲染限制')
        print('')
        print('🔍 请查看截图文件与您的手动截图进行最终对比确认')
    else:
        print('⚠️ PrintWindow方法仍未解决黑屏问题')
        print('💡 可能需要进一步的技术方案')

if __name__ == '__main__':
    test_printwindow_fix()
