Metadata-Version: 2.4
Name: desk-pilot
Version: 0.1.0
Summary: A Model Context Protocol (MCP) tool for Windows desktop automation and Tauri development assistance
Author: DeskPilot Team
License: MIT
Project-URL: Homepage, https://github.com/your-username/desk-pilot
Project-URL: Repository, https://github.com/your-username/desk-pilot
Project-URL: Documentation, https://github.com/your-username/desk-pilot/docs
Project-URL: Issues, https://github.com/your-username/desk-pilot/issues
Keywords: mcp,desktop-automation,tauri,windows,screenshot,ui-automation
Classifier: Development Status :: 3 - Alpha
Classifier: Intended Audience :: Developers
Classifier: Operating System :: Microsoft :: Windows
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: System :: Systems Administration
Requires-Python: >=3.12
Description-Content-Type: text/markdown
Requires-Dist: mcp>=1.2.0
Requires-Dist: pyautogui>=0.9.54
Requires-Dist: pygetwindow>=0.0.9
Requires-Dist: pillow>=10.0.0
Requires-Dist: rich>=13.0.0
Requires-Dist: setuptools>=61.0
Requires-Dist: wheel>=0.45.1
Provides-Extra: dev
Requires-Dist: pytest>=7.0.0; extra == "dev"
Requires-Dist: pytest-asyncio>=0.21.0; extra == "dev"
Requires-Dist: black>=23.0.0; extra == "dev"
Requires-Dist: isort>=5.12.0; extra == "dev"
Requires-Dist: mypy>=1.5.0; extra == "dev"

# 🚁 DeskPilot

A Model Context Protocol (MCP) tool for Windows desktop automation and Tauri development assistance.

## 🎯 Overview

DeskPilot enables AI assistants to interact with Windows desktop applications through the Model Context Protocol. It provides tools for:

- 📸 **Window Screenshot Capture** - Capture screenshots of specific windows or the active window
- 🖱️ **UI Automation** - Perform clicks, typing, key presses, and scrolling
- 🪟 **Window Management** - List and manage application windows
- ⚡ **Tauri Integration** - Create and develop Tauri applications with AI assistance
- 🎨 **Visual Mode** - Real-time operation effects for development and debugging
- 💻 **IDE Terminal Automation** - Automated IDE workflow for development tasks

## 🚀 Quick Start

### Prerequisites

- Python 3.12 or higher
- Windows operating system
- [uv](https://docs.astral.sh/uv/) package manager

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd desk-pilot
```

2. Install dependencies:
```bash
uv sync
```

3. Install the package:
```bash
uv pip install -e .
```

### Usage

Start the MCP server:
```bash
desk-pilot
```

Or with debug logging:
```bash
desk-pilot --debug
```

Enable visual mode for real-time operation effects:
```bash
desk-pilot --visual-mode
```

Custom operation delay in visual mode:
```bash
desk-pilot --visual-mode --operation-delay 1.0
```

Run the visual demo:
```bash
python visual_demo.py
```

## 🔧 Configuration

### Claude Desktop

Add to your Claude Desktop configuration file (`~/.claude/claude_desktop_config.json`):

```json
{
  "mcpServers": {
    "deskPilot": {
      "command": "desk-pilot",
      "args": ["--debug"]
    }
  }
}
```

### Other MCP Clients

DeskPilot works with any MCP-compatible client. Use the command `desk-pilot` as the server executable.

## 🛠️ Available Tools

### `capture-window`
Capture a screenshot of a specific window or the active window.

**Parameters:**
- `window_title` (optional): Title of the window to capture

**Example:**
```json
{
  "name": "capture-window",
  "arguments": {
    "window_title": "Notepad"
  }
}
```

### `ui-action`
Perform UI actions like clicking, typing, key presses, or scrolling.

**Parameters:**
- `action_type`: Type of action ("click", "type", "key", "scroll")
- `x`, `y`: Coordinates (for click/scroll)
- `text`: Text to type (for type action)
- `key`: Key to press (for key action)
- `button`: Mouse button ("left", "right", "middle")
- `clicks`: Number of clicks/scroll steps
- `interval`: Typing interval

**Examples:**
```json
{
  "name": "ui-action",
  "arguments": {
    "action_type": "click",
    "x": 100,
    "y": 200
  }
}
```

```json
{
  "name": "ui-action", 
  "arguments": {
    "action_type": "type",
    "text": "Hello, World!"
  }
}
```

### `list-windows`
Get a list of all visible windows with their properties.

### `get-screen-size`
Get the current screen dimensions.

### `tauri-create`
Create a new Tauri project.

**Parameters:**
- `name`: Project name
- `template` (optional): Template type (default: "vanilla")
- `directory` (optional): Directory to create the project in

### `tauri-dev`
Start a Tauri development server.

**Parameters:**
- `project_dir` (optional): Path to the Tauri project directory

### `set-visual-mode`
Enable or disable visual mode for real-time operation effects.

**Parameters:**
- `enabled`: Whether to enable visual mode
- `operation_delay` (optional): Delay between operations in seconds (default: 0.5)

### `get-mouse-position`
Get current mouse cursor position.

**Parameters:** None

### `automate-ide-terminal`
Automate IDE terminal workflow: find IDE window, open terminal, and execute command.

**Parameters:**
- `ide_window_title`: IDE window title or partial title to search for
- `command`: Command to execute in the terminal

## 🎨 Visual Mode

DeskPilot supports a **visual mode** that shows real-time operation effects, perfect for development, debugging, and demonstrations.

**Features:**
- Real-time mouse movement visualization
- Operation step logging
- Configurable delays between operations
- IDE terminal automation workflow

**Usage:**
```bash
# Enable visual mode with default settings
desk-pilot --visual-mode

# Custom delay for slower demonstration
desk-pilot --visual-mode --operation-delay 2.0
```

See [VISUAL_MODE_GUIDE.md](VISUAL_MODE_GUIDE.md) for detailed documentation.

## 🏗️ Development

### Project Structure

```
desk-pilot/
├── desk_pilot/
│   ├── __init__.py          # Package initialization
│   ├── __main__.py          # Command line entry point
│   ├── core.py              # Core desktop automation
│   ├── mcp_server.py        # MCP server implementation
│   └── tauri_integration.py # Tauri project integration
├── docs/
│   └── desk_pilot_design.md # Design documentation
├── tests/                   # Test files
├── pyproject.toml          # Project configuration
└── README.md               # This file
```

### Running Tests

```bash
uv run pytest
```

### Code Formatting

```bash
uv run black desk_pilot/
uv run isort desk_pilot/
```

## 📋 Requirements

### System Requirements
- Windows 10/11
- Python 3.12+
- Screen resolution: Any (automatically detected)

### For Tauri Development
- [Rust](https://rustup.rs/) and Cargo
- [Node.js](https://nodejs.org/) and npm
- Tauri CLI: `cargo install tauri-cli`

## 🔒 Security Considerations

DeskPilot can perform UI automation and access your desktop. When using with AI assistants:

- Review AI requests before allowing desktop interactions
- Use in trusted environments only
- Be cautious with sensitive applications
- Consider running in a virtual machine for testing

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.

## 🆘 Support

- 📖 [Documentation](docs/)
- 🐛 [Issue Tracker](https://github.com/your-username/desk-pilot/issues)
- 💬 [Discussions](https://github.com/your-username/desk-pilot/discussions)

## 🙏 Acknowledgments

- [Model Context Protocol](https://modelcontextprotocol.io/) by Anthropic
- [PyAutoGUI](https://pyautogui.readthedocs.io/) for desktop automation
- [Tauri](https://tauri.app/) for cross-platform app development
