#!/usr/bin/env python3
"""最终QuantumSeek截图测试 - 确保窗口可见"""

import pygetwindow as gw
from PIL import ImageGrab
import time
import win32gui
import win32con

def final_quantum_screenshot_test():
    print('🎯 最终QuantumSeek截图测试')
    print('=' * 50)
    
    # 1. 查找QuantumSeek窗口
    print('\n📍 步骤1: 查找QuantumSeek窗口')
    windows = gw.getWindowsWithTitle('QuantumSeek')
    
    if not windows:
        print('❌ 未找到QuantumSeek窗口')
        return
    
    window = windows[0]
    print(f'✅ 找到窗口: {window.title}')
    print(f'📍 位置: ({window.left}, {window.top})')
    print(f'📏 尺寸: {window.width} x {window.height}')
    
    # 2. 强制将窗口置于最前面
    print('\n🔝 步骤2: 将窗口置于最前面')
    try:
        # 使用Windows API将窗口置于最前面
        hwnd = win32gui.FindWindow(None, window.title)
        if hwnd:
            # 恢复窗口（如果最小化）
            win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
            time.sleep(0.5)
            
            # 将窗口置于最前面
            win32gui.SetWindowPos(hwnd, win32con.HWND_TOPMOST, 0, 0, 0, 0, 
                                win32con.SWP_NOMOVE | win32con.SWP_NOSIZE)
            time.sleep(0.5)
            
            # 激活窗口
            win32gui.SetForegroundWindow(hwnd)
            time.sleep(1)
            
            # 取消置顶（但保持在前面）
            win32gui.SetWindowPos(hwnd, win32con.HWND_NOTOPMOST, 0, 0, 0, 0, 
                                win32con.SWP_NOMOVE | win32con.SWP_NOSIZE)
            
            print('✅ 窗口已置于最前面')
        else:
            print('⚠️ 无法找到窗口句柄，使用pygetwindow方法')
            window.activate()
            time.sleep(2)
    except Exception as e:
        print(f'⚠️ 置顶窗口出错: {e}')
        try:
            window.activate()
            time.sleep(2)
        except:
            pass
    
    # 3. 重新获取窗口信息
    print('\n📏 步骤3: 重新获取窗口信息')
    windows = gw.getWindowsWithTitle('QuantumSeek')
    if windows:
        window = windows[0]
        print(f'📍 更新后位置: ({window.left}, {window.top})')
        print(f'📏 更新后尺寸: {window.width} x {window.height}')
        print(f'🔄 状态: 激活={window.isActive}, 最小化={window.isMinimized}')
    
    # 4. 等待一下确保窗口完全显示
    print('\n⏱️ 步骤4: 等待窗口完全显示')
    time.sleep(3)
    print('✅ 等待完成')
    
    # 5. 执行截图
    print('\n📸 步骤5: 执行截图')
    
    left = window.left
    top = window.top
    right = window.left + window.width
    bottom = window.top + window.height
    
    print(f'📏 截图区域: ({left}, {top}, {right}, {bottom})')
    
    try:
        # 使用PIL ImageGrab截图
        screenshot = ImageGrab.grab(bbox=(left, top, right, bottom))
        print(f'✅ 截图成功，尺寸: {screenshot.size}')
        
        # 保存截图
        filename = 'final_quantum_visible_screenshot.png'
        screenshot.save(filename)
        print(f'💾 截图已保存为: {filename}')
        
        # 6. 分析截图内容
        print('\n🔍 步骤6: 分析截图内容')
        
        colors = screenshot.getcolors(maxcolors=256*256*256)
        if colors:
            top_colors = sorted(colors, key=lambda x: x[0], reverse=True)[:10]
            total_pixels = screenshot.size[0] * screenshot.size[1]
            
            print('🎨 主要颜色分析:')
            for i, (count, color) in enumerate(top_colors):
                percentage = (count / total_pixels) * 100
                print(f'   {i+1:2d}. RGB{color} - {percentage:5.1f}%')
            
            # 检查截图质量
            is_mostly_black = (top_colors[0][1] == (0, 0, 0) and 
                             top_colors[0][0] > total_pixels * 0.8)
            
            if is_mostly_black:
                print('⚠️ 截图仍然主要是黑色')
            else:
                print('✅ 截图包含有效内容！')
                
                # 检查QuantumSeek特征
                features_found = []
                
                # 检查紫色主题
                for count, (r, g, b) in top_colors:
                    if 40 <= r <= 120 and 30 <= g <= 90 and 60 <= b <= 150:
                        features_found.append(f"紫色主题 RGB({r}, {g}, {b})")
                        break
                
                # 检查深色背景
                for count, (r, g, b) in top_colors:
                    if r < 50 and g < 50 and b < 50 and (r, g, b) != (0, 0, 0):
                        features_found.append(f"深色背景 RGB({r}, {g}, {b})")
                        break
                
                # 检查颜色多样性
                if len(top_colors) >= 5:
                    features_found.append("颜色多样性良好")
                
                print('\n🎯 QuantumSeek特征检测:')
                if features_found:
                    for feature in features_found:
                        print(f'   ✅ {feature}')
                else:
                    print('   ⚠️ 未检测到明显的QuantumSeek特征')
        
        # 7. 与用户截图对比
        print('\n📊 步骤7: 与用户截图对比')
        print('用户提供的截图特征:')
        print('   • 紫色主题界面')
        print('   • 左侧导航栏')
        print('   • 中央搜索区域')
        print('   • 底部统计信息')
        print('   • 整体深色主题')
        
        if not is_mostly_black:
            print('\n🎉 截图成功获取到有效内容！')
            print(f'📸 请查看文件: {filename}')
            print('🔍 建议用户对比此截图与手动截图的一致性')
        else:
            print('\n⚠️ 截图仍然无法获取有效内容')
            print('💡 可能需要手动确保QuantumSeek窗口完全可见')
        
    except Exception as e:
        print(f'❌ 截图失败: {e}')
    
    # 8. 总结
    print('\n📋 总结')
    print('=' * 50)
    print('已尝试的方法:')
    print('1. ✅ 使用Windows API将窗口置于最前面')
    print('2. ✅ 激活并聚焦窗口')
    print('3. ✅ 等待窗口完全显示')
    print('4. ✅ 使用PIL ImageGrab精确截图')
    print('')
    print('如果截图仍然是黑色，可能的原因:')
    print('• QuantumSeek使用了特殊的渲染方式')
    print('• 窗口内容需要用户交互才能显示')
    print('• 应用程序处于特殊状态')

if __name__ == '__main__':
    final_quantum_screenshot_test()
