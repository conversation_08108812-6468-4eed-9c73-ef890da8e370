#!/usr/bin/env python3
"""测试修复后的DeskPilot多显示器截图功能"""

from desk_pilot.core import DeskPilotCore
import base64
from PIL import Image
import io
import os

def test_deskpilot_multimonitor():
    print('🔍 测试修复后的DeskPilot多显示器截图功能...')

    # 创建核心实例
    core = DeskPilotCore(visual_mode=True)

    try:
        # 测试QuantumSeek窗口截图（使用实时窗口查找）
        print('📸 正在截取QuantumSeek窗口（使用实时坐标）...')
        result = core.capture_window('QuantumSeek')
        
        if isinstance(result, dict) and result.get('success'):
            base64_data = result['screenshot_base64']
            print('✅ 截图成功')
            print(f'📊 Base64长度: {len(base64_data)}')
            
            # 解码并保存截图
            image_data = base64.b64decode(base64_data)
            with open('deskpilot_fixed_screenshot.png', 'wb') as f:
                f.write(image_data)
            print('💾 截图已保存为: deskpilot_fixed_screenshot.png')
            
            # 分析图片内容
            img = Image.open(io.BytesIO(image_data))
            print(f'📏 截图尺寸: {img.size}')
            print(f'🎨 图片模式: {img.mode}')
            
            # 窗口信息
            window_info = result['window_info']
            print(f'🪟 QuantumSeek窗口信息:')
            print(f'   位置: ({window_info["left"]}, {window_info["top"]})')
            print(f'   尺寸: {window_info["width"]} x {window_info["height"]}')
            
            # 检查截图的主要颜色分布
            colors = img.getcolors(maxcolors=256*256*256)
            if colors:
                # 获取前10个最常见的颜色
                top_colors = sorted(colors, key=lambda x: x[0], reverse=True)[:10]
                print('\n🎨 截图中最常见的颜色:')
                for i, (count, color) in enumerate(top_colors):
                    percentage = (count / (img.size[0] * img.size[1])) * 100
                    print(f'   {i+1}. {color} - {percentage:.1f}%')
                    
                # 检查是否有紫色/粉色（QuantumSeek的主题色）- 更宽泛的检测
                has_purple = False
                purple_colors = []
                for count, (r, g, b) in top_colors:
                    # 更宽泛的紫色检测
                    if (r > 50 and b > 50 and (r + b) > g * 1.5) or \
                       (r > 80 and g > 30 and b > 80) or \
                       (r > 100 and g < r and b > 60) or \
                       (r > 200 and g > 100 and b > 150):  # 粉色
                        has_purple = True
                        purple_colors.append((r, g, b))
                
                if has_purple:
                    print(f'✅ 检测到紫色/粉色系颜色: {purple_colors}')
                    print('🎯 成功捕获QuantumSeek界面！')
                    print('🎉 DeskPilot多显示器截图修复成功！')
                else:
                    print('❌ 未检测到紫色/粉色，可能仍未包含QuantumSeek界面')
                    
                    # 检查是否主要是黑色
                    if top_colors[0][1] == (0, 0, 0) and top_colors[0][0] > img.size[0] * img.size[1] * 0.8:
                        print('⚠️ 截图主要是黑色，可能窗口被遮挡')
            
            # 检查文件大小
            file_size = os.path.getsize('deskpilot_fixed_screenshot.png')
            print(f'📁 截图文件大小: {file_size:,} 字节')
            
            # 多显示器处理分析
            if window_info['left'] >= 3840:
                print('\n🔧 多显示器处理分析:')
                print(f'   窗口位置: {window_info["left"]} (超出主屏宽度3840)')
                print('   应该使用了Windows API多显示器截图')
                if img.size[0] == window_info['width'] and img.size[1] == window_info['height']:
                    print(f'✅ 截图尺寸 {img.size} 匹配窗口尺寸，成功截取窗口区域')
                else:
                    print(f'⚠️ 截图尺寸 {img.size} 与窗口尺寸不匹配')
            
            print('\n🎯 请手动检查 deskpilot_fixed_screenshot.png')
            print('   应该能看到QuantumSeek的紫色界面，包括:')
            print('   - 左侧的搜索选项面板')
            print('   - 中央的搜索结果区域')
            print('   - 底部的统计信息')
            print('   - 紫色/粉色的UI主题色彩')
            
        else:
            print('❌ 截图失败')
            print(f'📄 错误信息: {result}')
            
    except Exception as e:
        print(f'❌ 测试失败: {str(e)}')
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_deskpilot_multimonitor()
