#!/usr/bin/env python3
"""激活窗口后进行精准截图测试"""

import logging
from desk_pilot.core import DeskPilotCore
import pygetwindow as gw
import base64
from PIL import Image
import io
import time

# 设置详细日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')

def test_activate_and_screenshot():
    print('🎯 激活窗口后精准截图测试')
    print('=' * 50)
    
    # 创建DeskPilot实例
    core = DeskPilotCore(visual_mode=True, operation_delay=1.0)
    
    # 1. 查找并激活QuantumSeek窗口
    print('\n📍 步骤1: 查找并激活QuantumSeek窗口')
    
    windows = gw.getWindowsWithTitle('QuantumSeek')
    if not windows:
        print('❌ 未找到QuantumSeek窗口')
        return
    
    window = windows[0]
    print(f'✅ 找到窗口: {window.title}')
    print(f'📍 当前状态: 激活={window.isActive}, 最小化={window.isMinimized}')
    
    # 激活窗口
    try:
        print('🔄 正在激活窗口...')
        if window.isMinimized:
            window.restore()
            time.sleep(1)
        
        window.activate()
        time.sleep(2)  # 等待窗口完全激活
        
        # 重新检查状态
        windows = gw.getWindowsWithTitle('QuantumSeek')
        if windows:
            window = windows[0]
            print(f'✅ 窗口激活后状态: 激活={window.isActive}, 最小化={window.isMinimized}')
        
    except Exception as e:
        print(f'⚠️ 激活窗口时出错: {e}')
    
    # 2. 获取窗口信息
    print('\n📏 步骤2: 获取窗口详细信息')
    window_info = core.get_window_info('QuantumSeek', refresh=True)
    
    if not window_info:
        print('❌ 无法获取窗口信息')
        return
    
    print(f'✅ 窗口信息:')
    print(f'   位置: ({window_info["left"]}, {window_info["top"]})')
    print(f'   尺寸: {window_info["width"]} x {window_info["height"]}')
    print(f'   激活: {window_info["is_active"]}')
    print(f'   最小化: {window_info["is_minimized"]}')
    
    # 3. 执行精准截图
    print('\n📸 步骤3: 执行精准截图')
    print('正在截取激活的QuantumSeek窗口...')
    
    result = core.capture_window('QuantumSeek', precise_region=True)
    
    if not result.get('success'):
        print(f'❌ 截图失败: {result.get("error")}')
        return
    
    print('✅ 截图成功！')
    
    # 4. 分析截图内容
    print('\n🔍 步骤4: 分析截图内容')
    
    # 解码截图
    image_data = base64.b64decode(result['screenshot_base64'])
    img = Image.open(io.BytesIO(image_data))
    
    print(f'📏 截图尺寸: {img.size}')
    
    # 保存截图
    filename = 'quantum_activated_screenshot.png'
    with open(filename, 'wb') as f:
        f.write(image_data)
    print(f'💾 截图已保存为: {filename}')
    
    # 颜色分析
    colors = img.getcolors(maxcolors=256*256*256)
    if colors:
        top_colors = sorted(colors, key=lambda x: x[0], reverse=True)[:5]
        total_pixels = img.size[0] * img.size[1]
        
        print('\n🎨 主要颜色:')
        for i, (count, color) in enumerate(top_colors):
            percentage = (count / total_pixels) * 100
            print(f'   {i+1}. RGB{color} - {percentage:5.1f}%')
        
        # 检查是否还是全黑
        if top_colors[0][1] == (0, 0, 0) and top_colors[0][0] > total_pixels * 0.9:
            print('⚠️ 截图仍然主要是黑色')
            
            # 尝试点击窗口中心来确保窗口内容可见
            print('\n🖱️ 尝试点击窗口中心激活内容...')
            click_result = core.perform_ui_action(
                action_type="click",
                window_title="QuantumSeek",
                x=window_info["center_x"],
                y=window_info["center_y"],
                relative_coords=False
            )
            
            if click_result["success"]:
                print('✅ 点击成功，等待界面刷新...')
                time.sleep(3)
                
                # 重新截图
                print('📸 重新截图...')
                result2 = core.capture_window('QuantumSeek', precise_region=True)
                
                if result2.get('success'):
                    image_data2 = base64.b64decode(result2['screenshot_base64'])
                    img2 = Image.open(io.BytesIO(image_data2))
                    
                    filename2 = 'quantum_after_click_screenshot.png'
                    with open(filename2, 'wb') as f:
                        f.write(image_data2)
                    print(f'💾 点击后截图保存为: {filename2}')
                    
                    # 分析点击后的颜色
                    colors2 = img2.getcolors(maxcolors=256*256*256)
                    if colors2:
                        top_colors2 = sorted(colors2, key=lambda x: x[0], reverse=True)[:5]
                        print('\n🎨 点击后主要颜色:')
                        for i, (count, color) in enumerate(top_colors2):
                            percentage = (count / total_pixels) * 100
                            print(f'   {i+1}. RGB{color} - {percentage:5.1f}%')
                        
                        # 检查是否有改善
                        if not (top_colors2[0][1] == (0, 0, 0) and top_colors2[0][0] > total_pixels * 0.9):
                            print('✅ 点击后截图内容有改善！')
                            img = img2  # 使用改善后的截图进行后续分析
                            filename = filename2
                        else:
                            print('⚠️ 点击后截图仍然主要是黑色')
        else:
            print('✅ 截图包含有效的彩色内容')
    
    # 5. 与用户截图对比
    print('\n📊 步骤5: 与用户截图对比')
    
    # 检查QuantumSeek特征
    has_purple = False
    has_variety = False
    
    if colors:
        # 检查颜色多样性
        if len(top_colors) > 3:
            has_variety = True
        
        # 检查是否有紫色系
        for count, (r, g, b) in top_colors:
            if 40 <= r <= 120 and 30 <= g <= 90 and 60 <= b <= 150:
                has_purple = True
                break
    
    print('用户截图特征对比:')
    print(f'   紫色主题: {"✅" if has_purple else "❌"}')
    print(f'   颜色多样性: {"✅" if has_variety else "❌"}')
    print(f'   非黑色内容: {"✅" if not (top_colors[0][1] == (0, 0, 0) and top_colors[0][0] > total_pixels * 0.9) else "❌"}')
    
    # 6. 总结
    print('\n📋 总结')
    print('=' * 50)
    print(f'📸 最终截图文件: {filename}')
    
    if has_purple and has_variety:
        print('✅ 截图可能与用户提供的手动截图相似')
        print('🔍 请用户查看截图文件进行最终确认')
    else:
        print('⚠️ 截图可能与用户提供的手动截图不完全一致')
        print('💡 可能的原因:')
        print('   • 窗口被其他窗口遮挡')
        print('   • 窗口内容需要时间加载')
        print('   • 窗口处于特殊状态')

if __name__ == '__main__':
    test_activate_and_screenshot()
