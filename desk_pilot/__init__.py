"""
DeskPilot - A Model Context Protocol (MCP) tool for Windows desktop automation.

This package provides tools for:
- Window screenshot capture
- UI interaction automation
- Desktop tool development support

Author: DeskPilot Team
License: MIT
"""

__version__ = "0.1.0"
__author__ = "DeskPilot Team"
__email__ = "<EMAIL>"

from .core import DeskPilotCore
from .mcp_server import DeskPilotMcpServer

__all__ = [
    "DeskPilotCore",
    "DeskPilotMcpServer",
    "__version__",
]
