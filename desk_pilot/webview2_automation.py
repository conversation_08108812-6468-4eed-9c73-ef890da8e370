"""
DeskPilot WebView2 Automation - 专门针对WebView2应用的自动化功能

使用Microsoft Edge WebDriver和Selenium框架对WebView2窗口进行精确的自动化操作。
这比传统的桌面截图+坐标点击方式更加精确和可靠。
"""

import base64
import io
import logging
import time
import psutil
import re
from typing import Dict, Any, Optional, List, Tuple
from pathlib import Path

try:
    from selenium import webdriver
    from selenium.webdriver.edge.options import Options as EdgeOptions
    from selenium.webdriver.edge.service import Service as EdgeService
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.common.action_chains import ActionChains
    from selenium.webdriver.common.keys import Keys
    from selenium.common.exceptions import (
        WebDriverException, TimeoutException, NoSuchElementException,
        ElementNotInteractableException, InvalidArgumentException
    )
    from webdriver_manager.microsoft import EdgeChromiumDriverManager
    from selenium.webdriver.chrome.service import Service as ChromeService
    from selenium.webdriver.chrome.options import Options as ChromeOptions
    from webdriver_manager.chrome import ChromeDriverManager
    HAS_SELENIUM = True
except ImportError as e:
    HAS_SELENIUM = False
    SELENIUM_ERROR = str(e)


class WebView2AutomationManager:
    """WebView2自动化管理器
    
    提供连接、控制和操作WebView2应用的功能。
    """
    
    def __init__(self, visual_mode: bool = True, operation_delay: float = 0.5):
        """初始化WebView2自动化管理器
        
        Args:
            visual_mode: 是否启用可视化模式
            operation_delay: 操作间隔时间（秒）
        """
        self.logger = logging.getLogger(__name__)
        self.visual_mode = visual_mode
        self.operation_delay = operation_delay
        self.driver = None
        self.connected_app = None
        self.debug_port = None
        
        if not HAS_SELENIUM:
            raise ImportError(
                f"Selenium dependencies not found: {SELENIUM_ERROR}. "
                "Please install with: uv add selenium webdriver-manager psutil"
            )
        
        if self.visual_mode:
            self.logger.info(f"WebView2 Automation Manager initialized with visual mode (delay: {operation_delay}s)")
        else:
            self.logger.info("WebView2 Automation Manager initialized")
    
    def detect_webview2_apps(self) -> Dict[str, Any]:
        """检测运行中的WebView2应用
        
        Returns:
            Dict: 包含检测到的WebView2应用信息
        """
        try:
            webview2_apps = []
            
            # 扫描所有进程，查找可能的WebView2应用
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    proc_info = proc.info
                    cmdline = proc_info.get('cmdline', [])
                    
                    if not cmdline:
                        continue
                    
                    # 查找包含remote-debugging-port参数的进程
                    debug_port = None
                    for arg in cmdline:
                        if '--remote-debugging-port=' in str(arg):
                            port_match = re.search(r'--remote-debugging-port=(\d+)', str(arg))
                            if port_match:
                                debug_port = int(port_match.group(1))
                                break
                    
                    # 查找可能的WebView2相关进程
                    name = proc_info.get('name', '').lower()
                    is_webview2_related = (
                        'webview2' in name or
                        'msedgewebview2' in name or
                        debug_port is not None or
                        any('webview2' in str(arg).lower() for arg in cmdline)
                    )
                    
                    if is_webview2_related:
                        app_info = {
                            'pid': proc_info['pid'],
                            'name': proc_info['name'],
                            'debug_port': debug_port,
                            'cmdline': ' '.join(cmdline) if cmdline else '',
                            'has_debug_port': debug_port is not None
                        }
                        webview2_apps.append(app_info)
                        
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
            
            self.logger.info(f"Detected {len(webview2_apps)} potential WebView2 applications")
            
            return {
                'success': True,
                'apps': webview2_apps,
                'count': len(webview2_apps)
            }
            
        except Exception as e:
            self.logger.error(f"Error detecting WebView2 apps: {e}")
            return {
                'success': False,
                'error': str(e),
                'apps': []
            }
    
    def connect_to_webview2(self, debug_port: int = None, app_path: str = None, 
                           launch_args: List[str] = None) -> Dict[str, Any]:
        """连接到WebView2应用
        
        Args:
            debug_port: 调试端口号，如果指定则连接到现有实例
            app_path: 应用程序路径，如果指定则启动新实例
            launch_args: 启动参数列表
            
        Returns:
            Dict: 连接结果
        """
        try:
            if self.driver:
                self.logger.warning("Already connected to a WebView2 instance. Disconnecting first.")
                self.disconnect()
            
            # 配置Edge选项 (最小化配置，确保兼容性)
            options = EdgeOptions()
            options.use_chromium = True

            # 移除所有可能不兼容的实验性选项
            # 只保留最基本的参数以确保兼容性
            options.add_argument('--disable-blink-features=AutomationControlled')
            
            if debug_port:
                # 连接到现有的WebView2实例
                options.debugger_address = f"localhost:{debug_port}"
                self.debug_port = debug_port
                
                if self.visual_mode:
                    self.logger.info(f"🔗 连接到现有WebView2实例 (端口: {debug_port})")
                
            elif app_path:
                # 启动新的WebView2应用
                if not Path(app_path).exists():
                    return {
                        'success': False,
                        'error': f"Application path not found: {app_path}"
                    }
                
                options.binary_location = app_path
                
                # 添加调试端口参数
                if not debug_port:
                    debug_port = 9222  # 默认端口
                
                options.add_argument(f'--remote-debugging-port={debug_port}')
                self.debug_port = debug_port
                
                # 添加其他启动参数
                if launch_args:
                    for arg in launch_args:
                        options.add_argument(arg)
                
                if self.visual_mode:
                    self.logger.info(f"🚀 启动WebView2应用: {app_path} (端口: {debug_port})")
            
            else:
                return {
                    'success': False,
                    'error': "Either debug_port or app_path must be specified"
                }
            
            # 设置WebDriver服务
            self.driver = None

            # 尝试多种WebDriver方案
            drivers_to_try = [
                ("Edge (Local)", lambda: self._create_edge_driver_local(options)),
                ("Edge (System)", lambda: self._create_edge_driver_system(options)),
                ("Edge (Auto-download)", lambda: self._create_edge_driver_auto(options)),
                ("Chrome (System)", lambda: self._create_chrome_driver_system(options)),
                ("Chrome (Auto-download)", lambda: self._create_chrome_driver_auto(options))
            ]

            last_error = None
            for driver_name, driver_func in drivers_to_try:
                try:
                    self.driver = driver_func()
                    self.logger.info(f"Successfully initialized {driver_name}")
                    break
                except Exception as e:
                    self.logger.warning(f"Failed to initialize {driver_name}: {e}")
                    last_error = e
                    continue

            if not self.driver:
                raise Exception(f"Cannot initialize any WebDriver. Last error: {last_error}")
            
            # 设置隐式等待
            self.driver.implicitly_wait(10)
            
            # 获取连接信息
            if debug_port:
                self.connected_app = f"WebView2 on port {debug_port}"
            else:
                self.connected_app = app_path
            
            if self.visual_mode:
                self.logger.info("✅ 成功连接到WebView2应用")
                time.sleep(self.operation_delay)
            
            return {
                'success': True,
                'connected_app': self.connected_app,
                'debug_port': self.debug_port,
                'driver_session_id': self.driver.session_id,
                'message': 'Successfully connected to WebView2 application'
            }
            
        except WebDriverException as e:
            self.logger.error(f"WebDriver error connecting to WebView2: {e}")
            return {
                'success': False,
                'error': f"WebDriver error: {str(e)}"
            }
        except Exception as e:
            self.logger.error(f"Error connecting to WebView2: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def disconnect(self) -> Dict[str, Any]:
        """断开WebView2连接
        
        Returns:
            Dict: 断开连接结果
        """
        try:
            if self.driver:
                if self.visual_mode:
                    self.logger.info("🔌 断开WebView2连接")
                
                self.driver.quit()
                self.driver = None
                self.connected_app = None
                self.debug_port = None
                
                if self.visual_mode:
                    self.logger.info("✅ 已断开WebView2连接")
                
                return {
                    'success': True,
                    'message': 'Disconnected from WebView2 application'
                }
            else:
                return {
                    'success': True,
                    'message': 'No active WebView2 connection'
                }
                
        except Exception as e:
            self.logger.error(f"Error disconnecting from WebView2: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _ensure_connected(self) -> bool:
        """确保已连接到WebView2实例"""
        if not self.driver:
            self.logger.error("Not connected to any WebView2 instance")
            return False
        return True

    def capture_screenshot(self) -> Dict[str, Any]:
        """捕获WebView2内容截图

        Returns:
            Dict: 包含截图base64编码的结果
        """
        try:
            if not self._ensure_connected():
                return {
                    'success': False,
                    'error': 'Not connected to WebView2 instance'
                }

            if self.visual_mode:
                self.logger.info("📸 捕获WebView2截图")

            # 使用WebDriver的截图功能
            screenshot_png = self.driver.get_screenshot_as_png()
            screenshot_base64 = base64.b64encode(screenshot_png).decode('utf-8')

            # 获取页面信息
            page_title = self.driver.title
            current_url = self.driver.current_url
            window_size = self.driver.get_window_size()

            if self.visual_mode:
                self.logger.info(f"✅ 截图完成 - 页面: {page_title}")
                time.sleep(self.operation_delay)

            return {
                'success': True,
                'screenshot_base64': screenshot_base64,
                'screenshot_format': 'PNG',
                'page_title': page_title,
                'current_url': current_url,
                'window_size': window_size,
                'connected_app': self.connected_app
            }

        except Exception as e:
            self.logger.error(f"Error capturing WebView2 screenshot: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def click_element(self, selector: str = None, text: str = None,
                     x: int = None, y: int = None, timeout: int = 10) -> Dict[str, Any]:
        """点击WebView2中的元素

        Args:
            selector: CSS选择器或XPath
            text: 元素文本内容
            x, y: 坐标位置（相对于WebView2内容）
            timeout: 等待超时时间

        Returns:
            Dict: 点击操作结果
        """
        try:
            if not self._ensure_connected():
                return {
                    'success': False,
                    'error': 'Not connected to WebView2 instance'
                }

            if self.visual_mode:
                if selector:
                    self.logger.info(f"🖱️ 点击元素: {selector}")
                elif text:
                    self.logger.info(f"🖱️ 点击文本: {text}")
                elif x is not None and y is not None:
                    self.logger.info(f"🖱️ 点击坐标: ({x}, {y})")

            element = None

            if x is not None and y is not None:
                # 坐标点击
                actions = ActionChains(self.driver)
                actions.move_by_offset(x, y).click().perform()

                if self.visual_mode:
                    self.logger.info(f"✅ 坐标点击完成: ({x}, {y})")
                    time.sleep(self.operation_delay)

                return {
                    'success': True,
                    'action': 'coordinate_click',
                    'coordinates': [x, y],
                    'message': f'Clicked at coordinates ({x}, {y})'
                }

            elif selector:
                # CSS选择器或XPath
                wait = WebDriverWait(self.driver, timeout)

                if selector.startswith('//') or selector.startswith('('):
                    # XPath
                    element = wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                else:
                    # CSS选择器
                    element = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, selector)))

            elif text:
                # 文本内容
                wait = WebDriverWait(self.driver, timeout)
                element = wait.until(EC.element_to_be_clickable((By.XPATH, f"//*[contains(text(), '{text}')]")))

            else:
                return {
                    'success': False,
                    'error': 'Must specify selector, text, or coordinates'
                }

            if element:
                # 滚动到元素可见
                self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                time.sleep(0.2)

                # 点击元素
                element.click()

                if self.visual_mode:
                    self.logger.info("✅ 元素点击完成")
                    time.sleep(self.operation_delay)

                return {
                    'success': True,
                    'action': 'element_click',
                    'selector': selector,
                    'text': text,
                    'element_tag': element.tag_name,
                    'element_text': element.text[:100] if element.text else '',
                    'message': 'Element clicked successfully'
                }

        except TimeoutException:
            error_msg = f"Element not found within {timeout} seconds"
            self.logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg
            }
        except ElementNotInteractableException as e:
            error_msg = f"Element not interactable: {str(e)}"
            self.logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg
            }
        except Exception as e:
            self.logger.error(f"Error clicking element: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def type_text(self, text: str, selector: str = None, clear_first: bool = True) -> Dict[str, Any]:
        """在WebView2中输入文本

        Args:
            text: 要输入的文本
            selector: 目标元素的CSS选择器或XPath
            clear_first: 是否先清空现有内容

        Returns:
            Dict: 输入操作结果
        """
        try:
            if not self._ensure_connected():
                return {
                    'success': False,
                    'error': 'Not connected to WebView2 instance'
                }

            if self.visual_mode:
                self.logger.info(f"⌨️ 输入文本: '{text[:50]}{'...' if len(text) > 50 else ''}'")

            if selector:
                # 查找指定元素
                wait = WebDriverWait(self.driver, 10)

                if selector.startswith('//') or selector.startswith('('):
                    element = wait.until(EC.presence_of_element_located((By.XPATH, selector)))
                else:
                    element = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))

                # 滚动到元素可见
                self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                time.sleep(0.2)

                # 点击元素获得焦点
                element.click()

                if clear_first:
                    element.clear()

                element.send_keys(text)

            else:
                # 在当前焦点元素中输入
                active_element = self.driver.switch_to.active_element

                if clear_first:
                    active_element.clear()

                active_element.send_keys(text)

            if self.visual_mode:
                self.logger.info("✅ 文本输入完成")
                time.sleep(self.operation_delay)

            return {
                'success': True,
                'action': 'type_text',
                'text': text,
                'selector': selector,
                'clear_first': clear_first,
                'message': 'Text input completed successfully'
            }

        except Exception as e:
            self.logger.error(f"Error typing text: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def execute_script(self, script: str, *args) -> Dict[str, Any]:
        """在WebView2中执行JavaScript代码

        Args:
            script: JavaScript代码
            *args: 传递给脚本的参数

        Returns:
            Dict: 脚本执行结果
        """
        try:
            if not self._ensure_connected():
                return {
                    'success': False,
                    'error': 'Not connected to WebView2 instance'
                }

            if self.visual_mode:
                self.logger.info(f"🔧 执行JavaScript: {script[:100]}{'...' if len(script) > 100 else ''}")

            result = self.driver.execute_script(script, *args)

            if self.visual_mode:
                self.logger.info("✅ JavaScript执行完成")
                time.sleep(self.operation_delay)

            return {
                'success': True,
                'action': 'execute_script',
                'script': script,
                'result': result,
                'message': 'JavaScript executed successfully'
            }

        except Exception as e:
            self.logger.error(f"Error executing script: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def get_page_source(self) -> Dict[str, Any]:
        """获取WebView2页面的HTML源码

        Returns:
            Dict: 页面源码信息
        """
        try:
            if not self._ensure_connected():
                return {
                    'success': False,
                    'error': 'Not connected to WebView2 instance'
                }

            if self.visual_mode:
                self.logger.info("📄 获取页面源码")

            page_source = self.driver.page_source
            page_title = self.driver.title
            current_url = self.driver.current_url

            if self.visual_mode:
                self.logger.info(f"✅ 页面源码获取完成 - 长度: {len(page_source)} 字符")
                time.sleep(self.operation_delay)

            return {
                'success': True,
                'page_source': page_source,
                'page_title': page_title,
                'current_url': current_url,
                'source_length': len(page_source),
                'message': 'Page source retrieved successfully'
            }

        except Exception as e:
            self.logger.error(f"Error getting page source: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def wait_for_element(self, selector: str, timeout: int = 10,
                        condition: str = 'presence') -> Dict[str, Any]:
        """等待元素出现或满足特定条件

        Args:
            selector: CSS选择器或XPath
            timeout: 等待超时时间
            condition: 等待条件 ('presence', 'visible', 'clickable')

        Returns:
            Dict: 等待结果
        """
        try:
            if not self._ensure_connected():
                return {
                    'success': False,
                    'error': 'Not connected to WebView2 instance'
                }

            if self.visual_mode:
                self.logger.info(f"⏳ 等待元素: {selector} (条件: {condition})")

            wait = WebDriverWait(self.driver, timeout)

            # 确定定位方式
            if selector.startswith('//') or selector.startswith('('):
                by = By.XPATH
            else:
                by = By.CSS_SELECTOR

            # 根据条件选择等待策略
            if condition == 'presence':
                element = wait.until(EC.presence_of_element_located((by, selector)))
            elif condition == 'visible':
                element = wait.until(EC.visibility_of_element_located((by, selector)))
            elif condition == 'clickable':
                element = wait.until(EC.element_to_be_clickable((by, selector)))
            else:
                return {
                    'success': False,
                    'error': f"Unknown condition: {condition}. Use 'presence', 'visible', or 'clickable'"
                }

            if self.visual_mode:
                self.logger.info("✅ 元素等待完成")
                time.sleep(self.operation_delay)

            return {
                'success': True,
                'action': 'wait_for_element',
                'selector': selector,
                'condition': condition,
                'timeout': timeout,
                'element_found': True,
                'element_tag': element.tag_name,
                'element_text': element.text[:100] if element.text else '',
                'message': f'Element found with condition: {condition}'
            }

        except TimeoutException:
            error_msg = f"Element '{selector}' not found within {timeout} seconds (condition: {condition})"
            self.logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg,
                'element_found': False
            }
        except Exception as e:
            self.logger.error(f"Error waiting for element: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def navigate_to_url(self, url: str) -> Dict[str, Any]:
        """导航到指定URL

        Args:
            url: 目标URL

        Returns:
            Dict: 导航结果
        """
        try:
            if not self._ensure_connected():
                return {
                    'success': False,
                    'error': 'Not connected to WebView2 instance'
                }

            if self.visual_mode:
                self.logger.info(f"🌐 导航到: {url}")

            self.driver.get(url)

            # 等待页面加载完成
            time.sleep(1)

            current_url = self.driver.current_url
            page_title = self.driver.title

            if self.visual_mode:
                self.logger.info(f"✅ 导航完成 - 页面: {page_title}")
                time.sleep(self.operation_delay)

            return {
                'success': True,
                'action': 'navigate',
                'target_url': url,
                'current_url': current_url,
                'page_title': page_title,
                'message': 'Navigation completed successfully'
            }

        except Exception as e:
            self.logger.error(f"Error navigating to URL: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def get_connection_status(self) -> Dict[str, Any]:
        """获取WebView2连接状态

        Returns:
            Dict: 连接状态信息
        """
        try:
            if self.driver:
                current_url = self.driver.current_url
                page_title = self.driver.title
                window_handles = self.driver.window_handles

                return {
                    'success': True,
                    'connected': True,
                    'connected_app': self.connected_app,
                    'debug_port': self.debug_port,
                    'current_url': current_url,
                    'page_title': page_title,
                    'window_count': len(window_handles),
                    'session_id': self.driver.session_id,
                    'visual_mode': self.visual_mode,
                    'operation_delay': self.operation_delay
                }
            else:
                return {
                    'success': True,
                    'connected': False,
                    'message': 'Not connected to any WebView2 instance'
                }

        except Exception as e:
            self.logger.error(f"Error getting connection status: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def _create_edge_driver_local(self, options):
        """使用本地下载的Edge WebDriver"""
        # 查找项目根目录下的msedgedriver.exe
        project_root = Path(__file__).parent.parent
        local_driver_path = project_root / "msedgedriver.exe"

        if local_driver_path.exists():
            service = EdgeService(executable_path=str(local_driver_path))
            return webdriver.Edge(service=service, options=options)
        else:
            raise Exception(f"Local Edge driver not found at: {local_driver_path}")

    def _create_edge_driver_system(self, options):
        """使用系统PATH中的Edge WebDriver"""
        service = EdgeService()
        return webdriver.Edge(service=service, options=options)

    def _create_edge_driver_auto(self, options):
        """使用自动下载的Edge WebDriver"""
        service = EdgeService(EdgeChromiumDriverManager().install())
        return webdriver.Edge(service=service, options=options)

    def _create_chrome_driver_system(self, options):
        """使用系统PATH中的Chrome WebDriver (作为WebView2的替代)"""
        # 将Edge选项转换为Chrome选项
        chrome_options = ChromeOptions()

        # 复制调试地址设置
        if hasattr(options, 'debugger_address'):
            chrome_options.debugger_address = options.debugger_address

        # 复制其他参数
        for arg in options.arguments:
            chrome_options.add_argument(arg)

        # 复制实验性选项
        for key, value in options.experimental_options.items():
            chrome_options.add_experimental_option(key, value)

        service = ChromeService()
        return webdriver.Chrome(service=service, options=chrome_options)

    def _create_chrome_driver_auto(self, options):
        """使用自动下载的Chrome WebDriver (作为WebView2的替代)"""
        # 将Edge选项转换为Chrome选项
        chrome_options = ChromeOptions()

        # 复制调试地址设置
        if hasattr(options, 'debugger_address'):
            chrome_options.debugger_address = options.debugger_address

        # 复制其他参数
        for arg in options.arguments:
            chrome_options.add_argument(arg)

        # 复制实验性选项
        for key, value in options.experimental_options.items():
            chrome_options.add_experimental_option(key, value)

        service = ChromeService(ChromeDriverManager().install())
        return webdriver.Chrome(service=service, options=chrome_options)
