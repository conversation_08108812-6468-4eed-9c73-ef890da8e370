#!/usr/bin/env python3
"""
基于Playwright的WebView2自动化模块
使用Playwright的connect_over_cdp功能连接WebView2应用
比Selenium方案更简单、更稳定、更现代
"""

import logging
import time
import psutil
import re
from typing import Dict, Any, Optional, List, Tuple
from pathlib import Path

try:
    from playwright.sync_api import sync_playwright, <PERSON><PERSON><PERSON>, BrowserContex<PERSON>, Page
    from playwright.sync_api import TimeoutError as PlaywrightTimeoutError
    HAS_PLAYWRIGHT = True
except ImportError:
    HAS_PLAYWRIGHT = False
    Browser = None
    BrowserContext = None
    Page = None
    PlaywrightTimeoutError = Exception


class PlaywrightWebView2Manager:
    """基于Playwright的WebView2自动化管理器"""
    
    def __init__(self, visual_mode: bool = True, operation_delay: float = 0.5):
        """
        初始化Playwright WebView2管理器
        
        Args:
            visual_mode: 是否启用可视化模式（显示操作过程）
            operation_delay: 操作间延迟时间（秒）
        """
        if not HAS_PLAYWRIGHT:
            raise ImportError("Playwright not installed. Run: pip install playwright")
        
        self.visual_mode = visual_mode
        self.operation_delay = operation_delay
        self.logger = logging.getLogger(__name__)
        
        # Playwright相关对象
        self.playwright = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.debug_port: Optional[int] = None
        
        self.logger.info(f"Playwright WebView2 Manager initialized with visual mode: {visual_mode}")
    
    def detect_webview2_apps(self) -> List[Dict[str, Any]]:
        """
        检测系统中运行的WebView2应用
        
        Returns:
            包含WebView2应用信息的列表
        """
        webview2_apps = []
        
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    proc_info = proc.info
                    if not proc_info['cmdline']:
                        continue
                    
                    cmdline = ' '.join(proc_info['cmdline'])
                    
                    # 检测WebView2相关进程
                    is_webview2 = (
                        'webview2' in proc_info['name'].lower() or
                        'msedgewebview2' in proc_info['name'].lower() or
                        '--remote-debugging-port' in cmdline
                    )
                    
                    if is_webview2:
                        # 提取调试端口
                        debug_port = None
                        port_match = re.search(r'--remote-debugging-port[=\s](\d+)', cmdline)
                        if port_match:
                            debug_port = int(port_match.group(1))
                        
                        app_info = {
                            'pid': proc_info['pid'],
                            'name': proc_info['name'],
                            'debug_port': debug_port,
                            'cmdline': cmdline[:100] + '...' if len(cmdline) > 100 else cmdline,
                            'has_debug_port': debug_port is not None
                        }
                        webview2_apps.append(app_info)
                        
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
        
        except Exception as e:
            self.logger.error(f"Error detecting WebView2 apps: {e}")
        
        self.logger.info(f"Detected {len(webview2_apps)} potential WebView2 applications")
        return webview2_apps
    
    def connect_to_webview2(self, debug_port: int, timeout: int = 30) -> Dict[str, Any]:
        """
        连接到WebView2应用

        Args:
            debug_port: WebView2应用的调试端口
            timeout: 连接超时时间（秒）

        Returns:
            连接结果字典
        """
        try:
            if self.browser:
                self.logger.warning("Already connected to a WebView2 instance. Disconnecting first.")
                self.disconnect()

            self.debug_port = debug_port
            cdp_url = f"http://localhost:{debug_port}"

            if self.visual_mode:
                self.logger.info(f"🔗 Connecting to WebView2 instance (port: {debug_port})")

            # 启动Playwright
            self.playwright = sync_playwright().start()

            # 通过CDP连接到WebView2（使用简化的连接方式）
            self.browser = self.playwright.chromium.connect_over_cdp(cdp_url)

            # 等待一下让连接稳定
            time.sleep(0.5)

            # 尝试获取页面的多种方法
            self.page = None
            self.context = None

            # 方法1：从现有contexts获取页面
            for ctx in self.browser.contexts:
                if ctx.pages:
                    self.context = ctx
                    self.page = ctx.pages[0]
                    break

            # 方法2：如果没有找到页面，尝试创建新的context和page
            if not self.page:
                try:
                    self.context = self.browser.new_context()
                    self.page = self.context.new_page()
                except Exception as e:
                    self.logger.warning(f"Cannot create new context/page: {e}")
                    # 再次尝试获取现有页面
                    for ctx in self.browser.contexts:
                        if ctx.pages:
                            self.context = ctx
                            self.page = ctx.pages[0]
                            break

            if not self.page:
                raise Exception("Cannot access any pages in WebView2 application")

            if self.visual_mode:
                self.logger.info("✅ Successfully connected to WebView2!")
                time.sleep(self.operation_delay)

            return {
                'success': True,
                'debug_port': debug_port,
                'url': self.page.url if self.page else None,
                'title': self.page.title() if self.page else None
            }

        except Exception as e:
            self.logger.error(f"Failed to connect to WebView2: {e}")
            # 清理部分连接
            try:
                if self.browser:
                    self.browser.close()
                if self.playwright:
                    self.playwright.stop()
            except:
                pass
            self.browser = None
            self.context = None
            self.page = None
            self.playwright = None

            return {
                'success': False,
                'error': str(e)
            }
    
    def disconnect(self) -> Dict[str, Any]:
        """
        断开WebView2连接
        
        Returns:
            断开连接结果字典
        """
        try:
            if self.page:
                self.page.close()
                self.page = None
            
            if self.context:
                self.context.close()
                self.context = None
            
            if self.browser:
                self.browser.close()
                self.browser = None
            
            if self.playwright:
                self.playwright.stop()
                self.playwright = None
            
            self.debug_port = None
            
            if self.visual_mode:
                self.logger.info("🔌 Disconnected from WebView2")
            
            return {'success': True}
            
        except Exception as e:
            self.logger.error(f"Error disconnecting from WebView2: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def capture_screenshot(self, filename: Optional[str] = None) -> Dict[str, Any]:
        """
        捕获WebView2页面截图
        
        Args:
            filename: 截图文件名（可选）
            
        Returns:
            截图结果字典
        """
        try:
            if not self.page:
                return {
                    'success': False,
                    'error': 'Not connected to WebView2'
                }
            
            if not filename:
                timestamp = int(time.time())
                filename = f"webview2_screenshot_{timestamp}.png"
            
            # 确保文件路径是绝对路径
            if not Path(filename).is_absolute():
                filename = str(Path.cwd() / filename)
            
            # 捕获截图
            self.page.screenshot(path=filename, full_page=True)
            
            if self.visual_mode:
                self.logger.info(f"📸 Screenshot saved: {filename}")
                time.sleep(self.operation_delay)
            
            return {
                'success': True,
                'screenshot_path': filename
            }
            
        except Exception as e:
            self.logger.error(f"Failed to capture screenshot: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def click_element(self, selector: str, timeout: int = 5000) -> Dict[str, Any]:
        """
        点击页面元素
        
        Args:
            selector: CSS选择器或文本选择器
            timeout: 等待超时时间（毫秒）
            
        Returns:
            点击结果字典
        """
        try:
            if not self.page:
                return {
                    'success': False,
                    'error': 'Not connected to WebView2'
                }
            
            # 等待元素可见并点击
            self.page.locator(selector).click(timeout=timeout)
            
            if self.visual_mode:
                self.logger.info(f"🖱️ Clicked element: {selector}")
                time.sleep(self.operation_delay)
            
            return {'success': True}
            
        except PlaywrightTimeoutError:
            return {
                'success': False,
                'error': f'Element not found or not clickable: {selector}'
            }
        except Exception as e:
            self.logger.error(f"Failed to click element: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def type_text(self, selector: str, text: str, timeout: int = 5000) -> Dict[str, Any]:
        """
        在指定元素中输入文本
        
        Args:
            selector: CSS选择器
            text: 要输入的文本
            timeout: 等待超时时间（毫秒）
            
        Returns:
            输入结果字典
        """
        try:
            if not self.page:
                return {
                    'success': False,
                    'error': 'Not connected to WebView2'
                }
            
            # 等待元素可见并输入文本
            element = self.page.locator(selector)
            element.click(timeout=timeout)  # 先点击获得焦点
            element.fill(text)  # 填入文本
            
            if self.visual_mode:
                self.logger.info(f"⌨️ Typed text in {selector}: {text}")
                time.sleep(self.operation_delay)
            
            return {'success': True}
            
        except PlaywrightTimeoutError:
            return {
                'success': False,
                'error': f'Element not found: {selector}'
            }
        except Exception as e:
            self.logger.error(f"Failed to type text: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def execute_script(self, script: str) -> Dict[str, Any]:
        """
        在WebView2页面中执行JavaScript代码

        Args:
            script: JavaScript代码

        Returns:
            执行结果字典
        """
        try:
            if not self.page:
                return {
                    'success': False,
                    'error': 'Not connected to WebView2'
                }

            result = self.page.evaluate(script)

            if self.visual_mode:
                self.logger.info(f"🔧 Executed JavaScript: {script[:50]}...")
                time.sleep(self.operation_delay)

            return {
                'success': True,
                'result': result
            }

        except Exception as e:
            self.logger.error(f"Failed to execute script: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def get_page_source(self) -> Dict[str, Any]:
        """
        获取WebView2页面的HTML源码

        Returns:
            页面源码结果字典
        """
        try:
            if not self.page:
                return {
                    'success': False,
                    'error': 'Not connected to WebView2'
                }

            source = self.page.content()

            return {
                'success': True,
                'source': source
            }

        except Exception as e:
            self.logger.error(f"Failed to get page source: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def wait_for_element(self, selector: str, timeout: int = 10000) -> Dict[str, Any]:
        """
        等待元素出现

        Args:
            selector: CSS选择器
            timeout: 等待超时时间（毫秒）

        Returns:
            等待结果字典
        """
        try:
            if not self.page:
                return {
                    'success': False,
                    'error': 'Not connected to WebView2'
                }

            self.page.locator(selector).wait_for(timeout=timeout)

            if self.visual_mode:
                self.logger.info(f"⏳ Element appeared: {selector}")
                time.sleep(self.operation_delay)

            return {'success': True}

        except PlaywrightTimeoutError:
            return {
                'success': False,
                'error': f'Element did not appear within timeout: {selector}'
            }
        except Exception as e:
            self.logger.error(f"Failed to wait for element: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def navigate_to_url(self, url: str, timeout: int = 30000) -> Dict[str, Any]:
        """
        导航到指定URL

        Args:
            url: 目标URL
            timeout: 导航超时时间（毫秒）

        Returns:
            导航结果字典
        """
        try:
            if not self.page:
                return {
                    'success': False,
                    'error': 'Not connected to WebView2'
                }

            self.page.goto(url, timeout=timeout)

            if self.visual_mode:
                self.logger.info(f"🌐 Navigated to: {url}")
                time.sleep(self.operation_delay)

            return {
                'success': True,
                'url': self.page.url,
                'title': self.page.title()
            }

        except PlaywrightTimeoutError:
            return {
                'success': False,
                'error': f'Navigation timeout: {url}'
            }
        except Exception as e:
            self.logger.error(f"Failed to navigate: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def get_status(self) -> Dict[str, Any]:
        """
        获取当前连接状态

        Returns:
            状态信息字典
        """
        try:
            if not self.page:
                return {
                    'connected': False,
                    'debug_port': None,
                    'url': None,
                    'title': None
                }

            return {
                'connected': True,
                'debug_port': self.debug_port,
                'url': self.page.url,
                'title': self.page.title(),
                'visual_mode': self.visual_mode
            }

        except Exception as e:
            self.logger.error(f"Failed to get status: {e}")
            return {
                'connected': False,
                'error': str(e)
            }
