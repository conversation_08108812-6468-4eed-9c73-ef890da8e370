"""
DeskPilot 命令行入口点

提供命令行接口启动MCP服务器。
"""

import argparse
import asyncio
import logging
import sys
from pathlib import Path

try:
    from rich.console import Console
    from rich.logging import <PERSON>Handler
    from rich.panel import Panel
    from rich.text import Text
except ImportError:
    print("Rich library not found. Please install with: uv add rich")
    sys.exit(1)

from . import __version__
from .mcp_server import DeskPilotMcpServer


def setup_logging(debug: bool = False) -> None:
    """设置日志配置

    Args:
        debug: 是否启用调试模式
    """
    level = logging.DEBUG if debug else logging.INFO

    # 在MCP模式下，所有输出都必须重定向到stderr
    # 因为stdout用于JSON-RPC通信
    logging.basicConfig(
        level=level,
        format="%(message)s",
        datefmt="[%X]",
        handlers=[RichHandler(rich_tracebacks=True, console=Console(stderr=True))]
    )

    # 设置第三方库的日志级别
    logging.getLogger("PIL").setLevel(logging.WARNING)
    logging.getLogger("pyautogui").setLevel(logging.WARNING)


def print_banner(console: Console) -> None:
    """打印启动横幅"""
    banner_text = Text()
    banner_text.append("DeskPilot", style="bold blue")
    banner_text.append(f" v{__version__}", style="dim")
    banner_text.append("\nModel Context Protocol Server for Windows Desktop Automation", style="dim")
    
    panel = Panel(
        banner_text,
        title="🚁 DeskPilot",
        title_align="left",
        border_style="blue"
    )
    
    console.print(panel)


def print_usage_info(console: Console) -> None:
    """打印使用说明"""
    usage_text = Text()
    usage_text.append("Available DeskPilot MCP Tools:\n", style="bold")
    usage_text.append("• deskpilot-capture-window", style="green")
    usage_text.append(" - Capture window screenshots\n")
    usage_text.append("• deskpilot-ui-action", style="green")
    usage_text.append(" - Perform UI actions (click, type, key, scroll)\n")
    usage_text.append("• deskpilot-list-windows", style="green")
    usage_text.append(" - List all visible windows\n")
    usage_text.append("• deskpilot-get-screen-size", style="green")
    usage_text.append(" - Get screen dimensions\n")
    usage_text.append("• deskpilot-set-visual-mode", style="green")
    usage_text.append(" - Enable/disable visual mode for real-time operation effects\n")
    usage_text.append("• deskpilot-get-mouse-position", style="green")
    usage_text.append(" - Get current mouse cursor position\n")
    usage_text.append("• deskpilot-automate-ide-terminal", style="green")
    usage_text.append(" - Automate IDE terminal workflow\n\n")

    usage_text.append("Configuration for AI clients:\n", style="bold")
    usage_text.append('{\n  "mcpServers": {\n    "DeskPilot": {\n      "command": "python",\n      "args": ["-m", "desk_pilot", "--debug"]\n    }\n  }\n}', style="dim")
    
    panel = Panel(
        usage_text,
        title="📖 Usage",
        title_align="left",
        border_style="green"
    )
    
    console.print(panel)


async def main_async(args: argparse.Namespace) -> None:
    """异步主函数

    Args:
        args: 命令行参数
    """
    # 在MCP模式下，所有控制台输出都必须重定向到stderr
    console = Console(stderr=True)

    # 设置日志
    setup_logging(args.debug)
    logger = logging.getLogger(__name__)

    # 打印横幅 - 只在非MCP模式下显示
    if not args.quiet:
        print_banner(console)

        if args.help_usage:
            print_usage_info(console)
            return

    try:
        # 创建并启动MCP服务器
        # visual_mode默认为True，除非用户指定--no-visual-mode
        visual_mode = not args.no_visual_mode
        server = DeskPilotMcpServer(
            visual_mode=visual_mode,
            operation_delay=args.operation_delay
        )

        if not args.quiet:
            console.print("🚀 Starting DeskPilot MCP Server...", style="bold green")
            console.print("💡 Connect this server to your AI client using MCP protocol", style="dim")
            console.print("🔧 Use Ctrl+C to stop the server", style="dim")
            console.print()

        logger.info("DeskPilot MCP Server starting...")

        # 运行服务器
        await server.run()

    except KeyboardInterrupt:
        if not args.quiet:
            console.print("\n👋 DeskPilot MCP Server stopped", style="yellow")
        logger.info("Server stopped by user")

    except Exception as e:
        logger.error(f"Server error: {e}")
        if not args.quiet:
            console.print(f"\n❌ Server error: {e}", style="red")
        sys.exit(1)


def main() -> None:
    """主函数 - 命令行入口点"""
    parser = argparse.ArgumentParser(
        description="DeskPilot - MCP Server for Windows Desktop Automation",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  desk-pilot                    # Start the MCP server (visual mode enabled by default)
  desk-pilot --debug           # Start with debug logging
  desk-pilot --no-visual-mode  # Start without visual mode
  desk-pilot --usage           # Show usage information
  desk-pilot --quiet           # Start without banner
        """
    )
    
    parser.add_argument(
        "--version",
        action="version",
        version=f"DeskPilot {__version__}"
    )
    
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug logging"
    )
    
    parser.add_argument(
        "--quiet",
        action="store_true", 
        help="Suppress banner and startup messages"
    )
    
    parser.add_argument(
        "--usage",
        dest="help_usage",
        action="store_true",
        help="Show detailed usage information and exit"
    )

    parser.add_argument(
        "--no-visual-mode",
        action="store_true",
        help="Disable visual mode (visual mode is enabled by default)"
    )

    parser.add_argument(
        "--operation-delay",
        type=float,
        default=0.5,
        help="Delay between operations in visual mode (seconds, default: 0.5)"
    )
    
    args = parser.parse_args()

    # 自动检测MCP环境 - 如果stdin不是终端，说明可能在MCP模式下运行
    # 在这种情况下自动启用quiet模式以避免污染stdout
    if not sys.stdin.isatty():
        args.quiet = True

    # 检查Python版本
    if sys.version_info < (3, 12):
        # 在MCP模式下，错误信息也要输出到stderr
        print("❌ DeskPilot requires Python 3.12 or higher", file=sys.stderr)
        print(f"   Current version: {sys.version}", file=sys.stderr)
        sys.exit(1)

    # 检查Windows平台
    if sys.platform != "win32":
        print("❌ DeskPilot is designed for Windows only", file=sys.stderr)
        print(f"   Current platform: {sys.platform}", file=sys.stderr)
        sys.exit(1)
    
    try:
        # 运行异步主函数
        asyncio.run(main_async(args))
    except KeyboardInterrupt:
        pass  # 已在main_async中处理


if __name__ == "__main__":
    main()
