"""
DeskPilot MCP Server - Model Context Protocol 服务器实现

提供MCP协议接口，让AI客户端可以调用桌面自动化功能。
"""

import asyncio
import logging
from typing import Any, Dict, Optional

try:
    import mcp.types as types
    from mcp.server import Server
    from mcp.server.stdio import stdio_server
except ImportError as e:
    raise ImportError(
        f"MCP SDK not found: {e}. "
        "Please install with: uv add mcp"
    )

from .core import DeskPilotCore

# 尝试导入WebView2自动化管理器（支持Simple、Playwright和Selenium三种实现）
try:
    from .simple_webview2 import SimpleWebView2Manager
    HAS_SIMPLE_WEBVIEW2 = True
except ImportError:
    HAS_SIMPLE_WEBVIEW2 = False
    SimpleWebView2Manager = None

try:
    from .playwright_webview2 import PlaywrightWebView2Manager
    HAS_PLAYWRIGHT_WEBVIEW2 = True
except ImportError:
    HAS_PLAYWRIGHT_WEBVIEW2 = False
    PlaywrightWebView2Manager = None

try:
    from .webview2_automation import WebView2AutomationManager
    HAS_SELENIUM_WEBVIEW2 = True
except ImportError:
    HAS_SELENIUM_WEBVIEW2 = False
    WebView2AutomationManager = None


class DeskPilotMcpServer:
    """DeskPilot MCP 服务器实现"""

    def __init__(self, visual_mode: bool = True, operation_delay: float = 0.5):
        """初始化MCP服务器

        Args:
            visual_mode: 是否启用可视化模式
            operation_delay: 操作间隔时间
        """
        self.logger = logging.getLogger(__name__)
        self.core = DeskPilotCore(visual_mode=visual_mode, operation_delay=operation_delay)

        # 初始化WebView2自动化管理器（优先使用Simple实现）
        self.webview2_manager = None
        self.webview2_available = False
        self.webview2_implementation = None

        # 优先使用Simple WebView2实现（最兼容）
        if HAS_SIMPLE_WEBVIEW2:
            try:
                self.webview2_manager = SimpleWebView2Manager(
                    visual_mode=visual_mode,
                    operation_delay=operation_delay
                )
                self.webview2_available = True
                self.webview2_implementation = "Simple CDP"
                self.logger.info("WebView2 automation manager initialized successfully (Simple CDP)")
            except Exception as e:
                self.logger.warning(f"Failed to initialize Simple WebView2 manager: {e}")

        # 如果Simple不可用，回退到Playwright实现
        if not self.webview2_available and HAS_PLAYWRIGHT_WEBVIEW2:
            try:
                self.webview2_manager = PlaywrightWebView2Manager(
                    visual_mode=visual_mode,
                    operation_delay=operation_delay
                )
                self.webview2_available = True
                self.webview2_implementation = "Playwright"
                self.logger.info("WebView2 automation manager initialized successfully (Playwright)")
            except Exception as e:
                self.logger.warning(f"Failed to initialize Playwright WebView2 manager: {e}")

        # 最后回退到Selenium实现
        if not self.webview2_available and HAS_SELENIUM_WEBVIEW2:
            try:
                self.webview2_manager = WebView2AutomationManager(
                    visual_mode=visual_mode,
                    operation_delay=operation_delay
                )
                self.webview2_available = True
                self.webview2_implementation = "Selenium"
                self.logger.info("WebView2 automation manager initialized successfully (Selenium)")
            except Exception as e:
                self.logger.warning(f"Failed to initialize Selenium WebView2 manager: {e}")

        if not self.webview2_available:
            self.logger.warning("WebView2 automation not available (no compatible implementation found)")
        else:
            self.logger.info(f"Using {self.webview2_implementation} for WebView2 automation")

        # 创建MCP服务器实例，使用更明确的名称和版本信息
        self.server = Server("DeskPilot")

        # 注册工具
        self._register_tools()

        mode_info = f" (可视化模式: {'启用' if visual_mode else '禁用'})"
        webview2_info = f", WebView2: {'可用' if self.webview2_available else '不可用'}"
        self.logger.info(f"DeskPilot MCP Server initialized{mode_info}{webview2_info}")
    
    def _register_tools(self):
        """注册 MCP 工具"""
        
        @self.server.list_tools()
        async def list_tools() -> list[types.Tool]:
            """列出所有可用的工具"""
            return [
                types.Tool(
                    name="deskpilot-capture-window",
                    description="[DeskPilot] Capture screenshot of a specific window or active window for desktop automation",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "window_title": {
                                "type": "string",
                                "description": "Title of the window to capture (optional, captures active window if not provided)"
                            }
                        }
                    }
                ),
                types.Tool(
                    name="deskpilot-ui-action",
                    description="[DeskPilot] Perform UI actions like click, type, key press, or scroll for desktop automation",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "action_type": {
                                "type": "string",
                                "enum": ["click", "type", "key", "scroll"],
                                "description": "Type of UI action to perform"
                            },
                            "x": {
                                "type": "number",
                                "description": "X coordinate (required for click and scroll)"
                            },
                            "y": {
                                "type": "number",
                                "description": "Y coordinate (required for click and scroll)"
                            },
                            "text": {
                                "type": "string",
                                "description": "Text to type (required for type action)"
                            },
                            "key": {
                                "type": "string",
                                "description": "Key to press (required for key action)"
                            },
                            "button": {
                                "type": "string",
                                "enum": ["left", "right", "middle"],
                                "description": "Mouse button for click action (default: left)"
                            },
                            "clicks": {
                                "type": "number",
                                "description": "Number of clicks/scroll steps (default: 1 for click, 3 for scroll)"
                            },
                            "interval": {
                                "type": "number",
                                "description": "Interval between characters when typing (default: 0.01)"
                            }
                        },
                        "required": ["action_type"]
                    }
                ),
                types.Tool(
                    name="deskpilot-list-windows",
                    description="[DeskPilot] Get a list of all visible windows with their titles and properties for desktop automation",
                    inputSchema={
                        "type": "object",
                        "properties": {}
                    }
                ),
                types.Tool(
                    name="deskpilot-get-screen-size",
                    description="[DeskPilot] Get the screen dimensions for desktop automation",
                    inputSchema={
                        "type": "object",
                        "properties": {}
                    }
                ),

                types.Tool(
                    name="deskpilot-set-visual-mode",
                    description="[DeskPilot] Enable or disable visual mode for desktop automation (shows real-time operation effects)",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "enabled": {
                                "type": "boolean",
                                "description": "Whether to enable visual mode",
                                "default": True
                            },
                            "operation_delay": {
                                "type": "number",
                                "description": "Delay between operations in seconds (default: 0.5)",
                                "default": 0.5
                            }
                        },
                        "required": ["enabled"]
                    }
                ),
                types.Tool(
                    name="deskpilot-get-mouse-position",
                    description="[DeskPilot] Get current mouse cursor position for desktop automation",
                    inputSchema={
                        "type": "object",
                        "properties": {}
                    }
                ),
                types.Tool(
                    name="deskpilot-automate-ide-terminal",
                    description="[DeskPilot] Automate IDE terminal workflow: find IDE window, open terminal, and execute command",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "ide_window_title": {
                                "type": "string",
                                "description": "IDE window title or partial title to search for"
                            },
                            "command": {
                                "type": "string",
                                "description": "Command to execute in the terminal"
                            }
                        },
                        "required": ["ide_window_title", "command"]
                    }
                ),
                # WebView2 自动化工具
                types.Tool(
                    name="webview2-detect-apps",
                    description="[WebView2] Detect running WebView2 applications and their debug ports",
                    inputSchema={
                        "type": "object",
                        "properties": {}
                    }
                ),
                types.Tool(
                    name="webview2-connect",
                    description="[WebView2] Connect to a WebView2 application for automation",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "debug_port": {
                                "type": "number",
                                "description": "Debug port of running WebView2 instance (optional)"
                            },
                            "app_path": {
                                "type": "string",
                                "description": "Path to WebView2 application to launch (optional)"
                            },
                            "launch_args": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "Additional launch arguments (optional)"
                            }
                        }
                    }
                ),
                types.Tool(
                    name="webview2-disconnect",
                    description="[WebView2] Disconnect from current WebView2 application",
                    inputSchema={
                        "type": "object",
                        "properties": {}
                    }
                ),
                types.Tool(
                    name="webview2-screenshot",
                    description="[WebView2] Capture screenshot of WebView2 content",
                    inputSchema={
                        "type": "object",
                        "properties": {}
                    }
                ),
                types.Tool(
                    name="webview2-click",
                    description="[WebView2] Click element in WebView2 application",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "selector": {
                                "type": "string",
                                "description": "CSS selector or XPath of element to click (optional)"
                            },
                            "text": {
                                "type": "string",
                                "description": "Text content of element to click (optional)"
                            },
                            "x": {
                                "type": "number",
                                "description": "X coordinate relative to WebView2 content (optional)"
                            },
                            "y": {
                                "type": "number",
                                "description": "Y coordinate relative to WebView2 content (optional)"
                            },
                            "timeout": {
                                "type": "number",
                                "description": "Wait timeout in seconds (default: 10)",
                                "default": 10
                            }
                        }
                    }
                ),
                types.Tool(
                    name="webview2-type",
                    description="[WebView2] Type text in WebView2 application",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "text": {
                                "type": "string",
                                "description": "Text to type"
                            },
                            "selector": {
                                "type": "string",
                                "description": "CSS selector or XPath of target element (optional)"
                            },
                            "clear_first": {
                                "type": "boolean",
                                "description": "Clear existing content first (default: true)",
                                "default": True
                            }
                        },
                        "required": ["text"]
                    }
                ),
                types.Tool(
                    name="webview2-execute-script",
                    description="[WebView2] Execute JavaScript code in WebView2 application",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "script": {
                                "type": "string",
                                "description": "JavaScript code to execute"
                            }
                        },
                        "required": ["script"]
                    }
                ),
                types.Tool(
                    name="webview2-get-page-source",
                    description="[WebView2] Get HTML source code of current page",
                    inputSchema={
                        "type": "object",
                        "properties": {}
                    }
                ),
                types.Tool(
                    name="webview2-wait-for-element",
                    description="[WebView2] Wait for element to appear or meet specific condition",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "selector": {
                                "type": "string",
                                "description": "CSS selector or XPath of element to wait for"
                            },
                            "timeout": {
                                "type": "number",
                                "description": "Wait timeout in seconds (default: 10)",
                                "default": 10
                            },
                            "condition": {
                                "type": "string",
                                "enum": ["presence", "visible", "clickable"],
                                "description": "Wait condition (default: presence)",
                                "default": "presence"
                            }
                        },
                        "required": ["selector"]
                    }
                ),
                types.Tool(
                    name="webview2-navigate",
                    description="[WebView2] Navigate to a specific URL",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "url": {
                                "type": "string",
                                "description": "URL to navigate to"
                            }
                        },
                        "required": ["url"]
                    }
                ),
                types.Tool(
                    name="webview2-status",
                    description="[WebView2] Get current connection status and page information",
                    inputSchema={
                        "type": "object",
                        "properties": {}
                    }
                ),
                # 新增的WebView2高级功能工具
                types.Tool(
                    name="webview2-find-element",
                    description="[WebView2] Find element(s) on the page using various selectors",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "selector": {
                                "type": "string",
                                "description": "Element selector string"
                            },
                            "selector_type": {
                                "type": "string",
                                "description": "Selector type: css, xpath, id, class, tag",
                                "default": "css"
                            },
                            "find_multiple": {
                                "type": "boolean",
                                "description": "Whether to find multiple elements",
                                "default": False
                            }
                        },
                        "required": ["selector"]
                    }
                ),
                types.Tool(
                    name="webview2-wait-for-element-advanced",
                    description="[WebView2] Wait for element with advanced conditions (present, visible, clickable, absent)",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "selector": {
                                "type": "string",
                                "description": "Element selector string"
                            },
                            "selector_type": {
                                "type": "string",
                                "description": "Selector type: css, xpath, id, class, tag",
                                "default": "css"
                            },
                            "condition": {
                                "type": "string",
                                "description": "Wait condition: present, visible, clickable, absent",
                                "default": "present"
                            },
                            "timeout": {
                                "type": "number",
                                "description": "Timeout in seconds",
                                "default": 10
                            }
                        },
                        "required": ["selector"]
                    }
                ),
                types.Tool(
                    name="webview2-scroll",
                    description="[WebView2] Scroll to specific position or element",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "x": {
                                "type": "number",
                                "description": "X coordinate to scroll to (optional)"
                            },
                            "y": {
                                "type": "number",
                                "description": "Y coordinate to scroll to (optional)"
                            },
                            "selector": {
                                "type": "string",
                                "description": "Element selector to scroll to (optional)"
                            },
                            "selector_type": {
                                "type": "string",
                                "description": "Selector type: css, xpath, id, class, tag",
                                "default": "css"
                            },
                            "behavior": {
                                "type": "string",
                                "description": "Scroll behavior: smooth, instant, auto",
                                "default": "smooth"
                            },
                            "delta_x": {
                                "type": "number",
                                "description": "Relative X scroll distance (for scroll by)"
                            },
                            "delta_y": {
                                "type": "number",
                                "description": "Relative Y scroll distance (for scroll by)"
                            }
                        }
                    }
                ),
                types.Tool(
                    name="webview2-hover",
                    description="[WebView2] Hover mouse over element",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "selector": {
                                "type": "string",
                                "description": "Element selector to hover over"
                            },
                            "selector_type": {
                                "type": "string",
                                "description": "Selector type: css, xpath, id, class, tag",
                                "default": "css"
                            },
                            "wait_timeout": {
                                "type": "number",
                                "description": "Wait timeout in seconds",
                                "default": 10
                            }
                        },
                        "required": ["selector"]
                    }
                ),
                types.Tool(
                    name="webview2-right-click",
                    description="[WebView2] Right-click element or coordinates",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "selector": {
                                "type": "string",
                                "description": "Element selector (optional)"
                            },
                            "selector_type": {
                                "type": "string",
                                "description": "Selector type: css, xpath, id, class, tag",
                                "default": "css"
                            },
                            "x": {
                                "type": "number",
                                "description": "X coordinate (optional)"
                            },
                            "y": {
                                "type": "number",
                                "description": "Y coordinate (optional)"
                            },
                            "wait_timeout": {
                                "type": "number",
                                "description": "Wait timeout in seconds",
                                "default": 10
                            }
                        }
                    }
                ),
                types.Tool(
                    name="webview2-send-key",
                    description="[WebView2] Send keyboard key with optional modifiers",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "key": {
                                "type": "string",
                                "description": "Key to send (e.g., Enter, Tab, Escape, F1, a, A)"
                            },
                            "modifiers": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "Modifier keys (Control, Shift, Alt)"
                            }
                        },
                        "required": ["key"]
                    }
                ),
                types.Tool(
                    name="webview2-screenshot-element",
                    description="[WebView2] Capture screenshot of specific element",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "selector": {
                                "type": "string",
                                "description": "Element selector to screenshot"
                            },
                            "selector_type": {
                                "type": "string",
                                "description": "Selector type: css, xpath, id, class, tag",
                                "default": "css"
                            },
                            "filename": {
                                "type": "string",
                                "description": "Screenshot filename (optional)"
                            },
                            "padding": {
                                "type": "number",
                                "description": "Padding around element in pixels",
                                "default": 10
                            }
                        },
                        "required": ["selector"]
                    }
                ),
                types.Tool(
                    name="webview2-screenshot-fullpage",
                    description="[WebView2] Capture full page screenshot",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "filename": {
                                "type": "string",
                                "description": "Screenshot filename (optional)"
                            }
                        }
                    }
                ),
                types.Tool(
                    name="webview2-page-navigate",
                    description="[WebView2] Navigate to URL or go back/forward",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "url": {
                                "type": "string",
                                "description": "URL to navigate to (optional)"
                            },
                            "action": {
                                "type": "string",
                                "description": "Navigation action: navigate, back, forward, reload",
                                "default": "navigate"
                            },
                            "ignore_cache": {
                                "type": "boolean",
                                "description": "Ignore cache when reloading",
                                "default": False
                            },
                            "wait_timeout": {
                                "type": "number",
                                "description": "Wait timeout for page load",
                                "default": 30
                            }
                        }
                    }
                )
            ]
        
        @self.server.call_tool()
        async def call_tool(name: str, arguments: dict) -> list[types.TextContent]:
            """处理工具调用"""
            try:
                if name == "deskpilot-capture-window":
                    window_title = arguments.get("window_title")
                    result = self.core.capture_window(window_title)

                    if result.get("success"):
                        return [
                            types.TextContent(
                                type="text",
                                text=f"[DeskPilot] Successfully captured window: {result['window_title']}\n"
                                     f"Window size: {result['window_info']['width']}x{result['window_info']['height']}\n"
                                     f"Screenshot format: {result['screenshot_format']}\n"
                                     f"Screenshot data: data:image/png;base64,{result['screenshot_base64']}"
                            )
                        ]
                    else:
                        return [
                            types.TextContent(
                                type="text",
                                text=f"[DeskPilot] Failed to capture window: {result.get('error', 'Unknown error')}"
                            )
                        ]

                elif name == "deskpilot-ui-action":
                    action_type = arguments.get("action_type")
                    if not action_type:
                        return [
                            types.TextContent(
                                type="text",
                                text="[DeskPilot] Error: action_type is required"
                            )
                        ]

                    # 从arguments中移除action_type，避免重复传递
                    ui_args = {k: v for k, v in arguments.items() if k != "action_type"}
                    result = self.core.perform_ui_action(action_type, **ui_args)

                    if result.get("success"):
                        return [
                            types.TextContent(
                                type="text",
                                text=f"[DeskPilot] Successfully performed {action_type} action: {result}"
                            )
                        ]
                    else:
                        return [
                            types.TextContent(
                                type="text",
                                text=f"[DeskPilot] Failed to perform {action_type} action: {result.get('error', 'Unknown error')}"
                            )
                        ]
                
                elif name == "deskpilot-list-windows":
                    result = self.core.get_window_list()

                    if result.get("success"):
                        windows = result.get("windows", [])
                        window_info = "\n".join([
                            f"- {w['title']} ({w['width']}x{w['height']}) {'[Active]' if w.get('is_active', False) else ''}"
                            for w in windows
                        ])

                        return [
                            types.TextContent(
                                type="text",
                                text=f"[DeskPilot] Found {len(windows)} windows:\n{window_info}"
                            )
                        ]
                    else:
                        return [
                            types.TextContent(
                                type="text",
                                text=f"[DeskPilot] Failed to get window list: {result.get('error', 'Unknown error')}"
                            )
                        ]

                elif name == "deskpilot-get-screen-size":
                    result = self.core.get_screen_size()

                    if result.get("success"):
                        return [
                            types.TextContent(
                                type="text",
                                text=f"[DeskPilot] Screen size: {result['width']}x{result['height']}"
                            )
                        ]
                    else:
                        return [
                            types.TextContent(
                                type="text",
                                text=f"[DeskPilot] Failed to get screen size: {result.get('error', 'Unknown error')}"
                            )
                        ]
                


                elif name == "deskpilot-set-visual-mode":
                    enabled = arguments.get("enabled", True)  # 默认启用可视化模式
                    operation_delay = arguments.get("operation_delay", 0.5)

                    result = self.core.set_visual_mode(enabled, operation_delay)

                    if result.get("success"):
                        return [
                            types.TextContent(
                                type="text",
                                text=f"[DeskPilot] Visual mode {'enabled' if enabled else 'disabled'}\n"
                                     f"Operation delay: {operation_delay}s\n"
                                     f"Message: {result.get('message', '')}"
                            )
                        ]
                    else:
                        return [
                            types.TextContent(
                                type="text",
                                text=f"[DeskPilot] Failed to set visual mode: {result.get('error', 'Unknown error')}"
                            )
                        ]

                elif name == "deskpilot-get-mouse-position":
                    result = self.core.get_mouse_position()

                    if result.get("success"):
                        return [
                            types.TextContent(
                                type="text",
                                text=f"[DeskPilot] Current mouse position: ({result['x']}, {result['y']})"
                            )
                        ]
                    else:
                        return [
                            types.TextContent(
                                type="text",
                                text=f"[DeskPilot] Failed to get mouse position: {result.get('error', 'Unknown error')}"
                            )
                        ]

                elif name == "deskpilot-automate-ide-terminal":
                    ide_window_title = arguments.get("ide_window_title")
                    command = arguments.get("command")

                    result = self.core.automate_ide_terminal_workflow(ide_window_title, command)

                    if result.get("success"):
                        workflow_steps = "\n".join([f"  • {step}" for step in result.get('workflow_steps', [])])
                        return [
                            types.TextContent(
                                type="text",
                                text=f"[DeskPilot] IDE Terminal Workflow Completed Successfully\n"
                                     f"IDE Window: {result.get('ide_window', 'Unknown')}\n"
                                     f"Command: {result.get('command', 'Unknown')}\n"
                                     f"Workflow Steps:\n{workflow_steps}\n"
                                     f"Message: {result.get('message', '')}"
                            )
                        ]
                    else:
                        workflow_steps = "\n".join([f"  • {step}" for step in result.get('workflow_steps', [])])
                        return [
                            types.TextContent(
                                type="text",
                                text=f"[DeskPilot] IDE Terminal Workflow Failed\n"
                                     f"Error: {result.get('error', 'Unknown error')}\n"
                                     f"Completed Steps:\n{workflow_steps}"
                            )
                        ]

                # WebView2 工具处理
                elif name.startswith("webview2-"):
                    if not self.webview2_available:
                        return [
                            types.TextContent(
                                type="text",
                                text="[WebView2] WebView2 automation not available. Please install dependencies: uv add selenium webdriver-manager psutil"
                            )
                        ]

                    return await self._handle_webview2_tool(name, arguments)

                else:
                    return [
                        types.TextContent(
                            type="text",
                            text=f"Unknown tool: {name}"
                        )
                    ]
                    
            except Exception as e:
                self.logger.error(f"Error calling tool {name}: {e}")
                return [
                    types.TextContent(
                        type="text",
                        text=f"Error calling tool {name}: {str(e)}"
                    )
                ]

    async def _handle_webview2_tool(self, name: str, arguments: dict) -> list[types.TextContent]:
        """处理WebView2工具调用"""
        try:
            if name == "webview2-detect-apps":
                result = self.webview2_manager.detect_webview2_apps()

                if result.get("success"):
                    apps_info = []
                    for app in result.get("apps", []):
                        debug_status = "✅ 有调试端口" if app.get("has_debug_port") else "❌ 无调试端口"
                        apps_info.append(
                            f"  • PID: {app['pid']}, 名称: {app['name']}, "
                            f"调试端口: {app.get('debug_port', 'N/A')}, {debug_status}"
                        )

                    apps_text = "\n".join(apps_info) if apps_info else "  未检测到WebView2应用"

                    return [
                        types.TextContent(
                            type="text",
                            text=f"[WebView2] 检测到 {result.get('count', 0)} 个潜在的WebView2应用:\n{apps_text}"
                        )
                    ]
                else:
                    return [
                        types.TextContent(
                            type="text",
                            text=f"[WebView2] 检测WebView2应用失败: {result.get('error', 'Unknown error')}"
                        )
                    ]

            elif name == "webview2-connect":
                debug_port = arguments.get("debug_port")
                app_path = arguments.get("app_path")
                launch_args = arguments.get("launch_args")

                result = self.webview2_manager.connect_to_webview2(
                    debug_port=debug_port,
                    app_path=app_path,
                    launch_args=launch_args
                )

                if result.get("success"):
                    return [
                        types.TextContent(
                            type="text",
                            text=f"[WebView2] 连接成功!\n"
                                 f"应用: {result.get('connected_app', 'Unknown')}\n"
                                 f"调试端口: {result.get('debug_port', 'N/A')}\n"
                                 f"会话ID: {result.get('driver_session_id', 'N/A')}\n"
                                 f"消息: {result.get('message', '')}"
                        )
                    ]
                else:
                    return [
                        types.TextContent(
                            type="text",
                            text=f"[WebView2] 连接失败: {result.get('error', 'Unknown error')}"
                        )
                    ]

            elif name == "webview2-disconnect":
                result = self.webview2_manager.disconnect()

                return [
                    types.TextContent(
                        type="text",
                        text=f"[WebView2] {result.get('message', '断开连接完成')}"
                    )
                ]

            elif name == "webview2-screenshot":
                result = self.webview2_manager.capture_screenshot()

                if result.get("success"):
                    return [
                        types.TextContent(
                            type="text",
                            text=f"[WebView2] 截图成功!\n"
                                 f"页面标题: {result.get('page_title', 'Unknown')}\n"
                                 f"当前URL: {result.get('current_url', 'Unknown')}\n"
                                 f"窗口大小: {result.get('window_size', {}).get('width', 'N/A')}x{result.get('window_size', {}).get('height', 'N/A')}\n"
                                 f"截图格式: {result.get('screenshot_format', 'PNG')}\n"
                                 f"截图数据: data:image/png;base64,{result.get('screenshot_base64', '')}"
                        )
                    ]
                else:
                    return [
                        types.TextContent(
                            type="text",
                            text=f"[WebView2] 截图失败: {result.get('error', 'Unknown error')}"
                        )
                    ]

            elif name == "webview2-click":
                selector = arguments.get("selector")
                text = arguments.get("text")
                x = arguments.get("x")
                y = arguments.get("y")
                timeout = arguments.get("timeout", 10)

                result = self.webview2_manager.click_element(
                    selector=selector,
                    text=text,
                    x=x,
                    y=y,
                    timeout=timeout
                )

                if result.get("success"):
                    action_type = result.get("action", "click")
                    if action_type == "coordinate_click":
                        coords = result.get("coordinates", [])
                        return [
                            types.TextContent(
                                type="text",
                                text=f"[WebView2] 坐标点击成功: ({coords[0]}, {coords[1]})"
                            )
                        ]
                    else:
                        element_info = f"标签: {result.get('element_tag', 'N/A')}, 文本: {result.get('element_text', 'N/A')[:50]}"
                        return [
                            types.TextContent(
                                type="text",
                                text=f"[WebView2] 元素点击成功!\n"
                                     f"选择器: {result.get('selector', 'N/A')}\n"
                                     f"元素信息: {element_info}\n"
                                     f"消息: {result.get('message', '')}"
                            )
                        ]
                else:
                    return [
                        types.TextContent(
                            type="text",
                            text=f"[WebView2] 点击失败: {result.get('error', 'Unknown error')}"
                        )
                    ]

            elif name == "webview2-type":
                text = arguments.get("text")
                selector = arguments.get("selector")
                clear_first = arguments.get("clear_first", True)

                result = self.webview2_manager.type_text(
                    text=text,
                    selector=selector,
                    clear_first=clear_first
                )

                if result.get("success"):
                    return [
                        types.TextContent(
                            type="text",
                            text=f"[WebView2] 文本输入成功!\n"
                                 f"输入文本: '{result.get('text', '')[:100]}{'...' if len(result.get('text', '')) > 100 else ''}'\n"
                                 f"目标选择器: {result.get('selector', '当前焦点元素')}\n"
                                 f"清空现有内容: {'是' if result.get('clear_first') else '否'}"
                        )
                    ]
                else:
                    return [
                        types.TextContent(
                            type="text",
                            text=f"[WebView2] 文本输入失败: {result.get('error', 'Unknown error')}"
                        )
                    ]

            elif name == "webview2-execute-script":
                script = arguments.get("script")

                result = self.webview2_manager.execute_script(script)

                if result.get("success"):
                    script_result = result.get("result")
                    result_text = str(script_result) if script_result is not None else "无返回值"

                    return [
                        types.TextContent(
                            type="text",
                            text=f"[WebView2] JavaScript执行成功!\n"
                                 f"脚本: {result.get('script', '')[:100]}{'...' if len(result.get('script', '')) > 100 else ''}\n"
                                 f"返回结果: {result_text[:200]}{'...' if len(result_text) > 200 else ''}"
                        )
                    ]
                else:
                    return [
                        types.TextContent(
                            type="text",
                            text=f"[WebView2] JavaScript执行失败: {result.get('error', 'Unknown error')}"
                        )
                    ]

            elif name == "webview2-get-page-source":
                result = self.webview2_manager.get_page_source()

                if result.get("success"):
                    source_length = result.get("source_length", 0)
                    return [
                        types.TextContent(
                            type="text",
                            text=f"[WebView2] 页面源码获取成功!\n"
                                 f"页面标题: {result.get('page_title', 'Unknown')}\n"
                                 f"当前URL: {result.get('current_url', 'Unknown')}\n"
                                 f"源码长度: {source_length} 字符\n"
                                 f"页面源码:\n{result.get('page_source', '')}"
                        )
                    ]
                else:
                    return [
                        types.TextContent(
                            type="text",
                            text=f"[WebView2] 页面源码获取失败: {result.get('error', 'Unknown error')}"
                        )
                    ]

            elif name == "webview2-wait-for-element":
                selector = arguments.get("selector")
                timeout = arguments.get("timeout", 10)
                condition = arguments.get("condition", "presence")

                result = self.webview2_manager.wait_for_element(
                    selector=selector,
                    timeout=timeout,
                    condition=condition
                )

                if result.get("success"):
                    element_info = f"标签: {result.get('element_tag', 'N/A')}, 文本: {result.get('element_text', 'N/A')[:50]}"
                    return [
                        types.TextContent(
                            type="text",
                            text=f"[WebView2] 元素等待成功!\n"
                                 f"选择器: {result.get('selector', 'N/A')}\n"
                                 f"等待条件: {result.get('condition', 'N/A')}\n"
                                 f"超时时间: {result.get('timeout', 'N/A')}秒\n"
                                 f"元素信息: {element_info}"
                        )
                    ]
                else:
                    return [
                        types.TextContent(
                            type="text",
                            text=f"[WebView2] 元素等待失败: {result.get('error', 'Unknown error')}"
                        )
                    ]

            elif name == "webview2-navigate":
                url = arguments.get("url")

                result = self.webview2_manager.navigate_to_url(url)

                if result.get("success"):
                    return [
                        types.TextContent(
                            type="text",
                            text=f"[WebView2] 导航成功!\n"
                                 f"目标URL: {result.get('target_url', 'N/A')}\n"
                                 f"当前URL: {result.get('current_url', 'N/A')}\n"
                                 f"页面标题: {result.get('page_title', 'N/A')}"
                        )
                    ]
                else:
                    return [
                        types.TextContent(
                            type="text",
                            text=f"[WebView2] 导航失败: {result.get('error', 'Unknown error')}"
                        )
                    ]

            elif name == "webview2-status":
                result = self.webview2_manager.get_connection_status()

                if result.get("success"):
                    if result.get("connected"):
                        return [
                            types.TextContent(
                                type="text",
                                text=f"[WebView2] 连接状态: ✅ 已连接\n"
                                     f"连接应用: {result.get('connected_app', 'Unknown')}\n"
                                     f"调试端口: {result.get('debug_port', 'N/A')}\n"
                                     f"当前URL: {result.get('current_url', 'N/A')}\n"
                                     f"页面标题: {result.get('page_title', 'N/A')}\n"
                                     f"窗口数量: {result.get('window_count', 'N/A')}\n"
                                     f"会话ID: {result.get('session_id', 'N/A')}\n"
                                     f"可视化模式: {'启用' if result.get('visual_mode') else '禁用'}\n"
                                     f"操作延迟: {result.get('operation_delay', 'N/A')}秒"
                            )
                        ]
                    else:
                        return [
                            types.TextContent(
                                type="text",
                                text=f"[WebView2] 连接状态: ❌ 未连接\n{result.get('message', '')}"
                            )
                        ]
                else:
                    return [
                        types.TextContent(
                            type="text",
                            text=f"[WebView2] 获取状态失败: {result.get('error', 'Unknown error')}"
                        )
                    ]

            else:
                return [
                    types.TextContent(
                        type="text",
                        text=f"[WebView2] 未知的WebView2工具: {name}"
                    )
                ]

        except Exception as e:
            self.logger.error(f"Error handling WebView2 tool {name}: {e}")
            return [
                types.TextContent(
                    type="text",
                    text=f"[WebView2] 处理工具 {name} 时出错: {str(e)}"
                )
            ]

    async def run(self):
        """运行MCP服务器"""
        try:
            async with stdio_server() as (read_stream, write_stream):
                self.logger.info("Starting DeskPilot MCP Server...")
                await self.server.run(
                    read_stream,
                    write_stream,
                    self.server.create_initialization_options()
                )
        except Exception as e:
            self.logger.error(f"Error running MCP server: {e}")
            raise
