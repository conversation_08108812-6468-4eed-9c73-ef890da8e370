#!/usr/bin/env python3
"""
简化的WebView2自动化模块
直接使用Chrome DevTools Protocol，避免Playwright的高级功能限制
"""

import json
import logging
import time
import requests
import websocket
import threading
from typing import Dict, Any, Optional, List
import psutil
import re
from pathlib import Path

class SimpleWebView2Manager:
    """简化的WebView2自动化管理器，直接使用CDP"""
    
    def __init__(self, visual_mode: bool = True, operation_delay: float = 0.5):
        """
        初始化简化WebView2管理器
        
        Args:
            visual_mode: 是否启用可视化模式
            operation_delay: 操作间延迟时间（秒）
        """
        self.visual_mode = visual_mode
        self.operation_delay = operation_delay
        self.logger = logging.getLogger(__name__)
        
        # CDP连接相关
        self.debug_port = None
        self.ws = None
        self.page_id = None
        self.session_id = None
        self.message_id = 0
        
        self.logger.info(f"Simple WebView2 Manager initialized with visual mode: {visual_mode}")
    
    def detect_webview2_apps(self) -> List[Dict[str, Any]]:
        """
        检测系统中运行的WebView2应用
        
        Returns:
            包含WebView2应用信息的列表
        """
        webview2_apps = []
        
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    proc_info = proc.info
                    if not proc_info['cmdline']:
                        continue
                    
                    cmdline = ' '.join(proc_info['cmdline'])
                    
                    # 检测WebView2相关进程
                    is_webview2 = (
                        'webview2' in proc_info['name'].lower() or
                        'msedgewebview2' in proc_info['name'].lower() or
                        '--remote-debugging-port' in cmdline
                    )
                    
                    if is_webview2:
                        # 提取调试端口
                        debug_port = None
                        port_match = re.search(r'--remote-debugging-port[=\s](\d+)', cmdline)
                        if port_match:
                            debug_port = int(port_match.group(1))
                        
                        app_info = {
                            'pid': proc_info['pid'],
                            'name': proc_info['name'],
                            'debug_port': debug_port,
                            'cmdline': cmdline[:100] + '...' if len(cmdline) > 100 else cmdline,
                            'has_debug_port': debug_port is not None
                        }
                        webview2_apps.append(app_info)
                        
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
        
        except Exception as e:
            self.logger.error(f"Error detecting WebView2 apps: {e}")
        
        self.logger.info(f"Detected {len(webview2_apps)} potential WebView2 applications")
        return webview2_apps
    
    def connect_to_webview2(self, debug_port: int, timeout: int = 30) -> Dict[str, Any]:
        """
        连接到WebView2应用
        
        Args:
            debug_port: WebView2应用的调试端口
            timeout: 连接超时时间（秒）
            
        Returns:
            连接结果字典
        """
        try:
            if self.ws:
                self.logger.warning("Already connected to a WebView2 instance. Disconnecting first.")
                self.disconnect()
            
            self.debug_port = debug_port
            
            if self.visual_mode:
                self.logger.info(f"🔗 Connecting to WebView2 instance (port: {debug_port})")
            
            # 1. 获取可用的页面列表
            list_url = f"http://localhost:{debug_port}/json/list"
            response = requests.get(list_url, timeout=10)
            response.raise_for_status()
            
            pages = response.json()
            if not pages:
                raise Exception("No pages found in WebView2 application")
            
            # 选择第一个可用页面
            target_page = None
            for page in pages:
                if page.get('type') == 'page' and 'webSocketDebuggerUrl' in page:
                    target_page = page
                    break
            
            if not target_page:
                # 如果没有找到page类型，尝试使用第一个有websocket的
                for page in pages:
                    if 'webSocketDebuggerUrl' in page:
                        target_page = page
                        break
            
            if not target_page:
                raise Exception("No suitable page found for connection")
            
            self.page_id = target_page['id']
            ws_url = target_page['webSocketDebuggerUrl']
            
            if self.visual_mode:
                self.logger.info(f"📄 Found page: {target_page.get('title', 'Unknown')} - {target_page.get('url', 'Unknown')}")
            
            # 2. 建立WebSocket连接
            self.ws = websocket.create_connection(ws_url, timeout=timeout)
            
            # 3. 启用必要的域
            self._send_command("Runtime.enable")
            self._send_command("Page.enable")
            
            if self.visual_mode:
                self.logger.info("✅ Successfully connected to WebView2!")
                time.sleep(self.operation_delay)
            
            return {
                'success': True,
                'debug_port': debug_port,
                'page_id': self.page_id,
                'url': target_page.get('url'),
                'title': target_page.get('title')
            }
            
        except Exception as e:
            self.logger.error(f"Failed to connect to WebView2: {e}")
            self._cleanup_connection()
            return {
                'success': False,
                'error': str(e)
            }
    
    def _send_command(self, method: str, params: Optional[Dict] = None) -> Dict[str, Any]:
        """发送CDP命令"""
        if not self.ws:
            raise Exception("Not connected to WebView2")
        
        self.message_id += 1
        message = {
            "id": self.message_id,
            "method": method
        }
        if params:
            message["params"] = params
        
        self.ws.send(json.dumps(message))
        
        # 等待响应
        response = self.ws.recv()
        return json.loads(response)
    
    def _cleanup_connection(self):
        """清理连接"""
        if self.ws:
            try:
                self.ws.close()
            except:
                pass
            self.ws = None
        self.page_id = None
        self.session_id = None
        self.message_id = 0
    
    def disconnect(self) -> Dict[str, Any]:
        """
        断开WebView2连接
        
        Returns:
            断开连接结果字典
        """
        try:
            self._cleanup_connection()
            self.debug_port = None
            
            if self.visual_mode:
                self.logger.info("🔌 Disconnected from WebView2")
            
            return {'success': True}
            
        except Exception as e:
            self.logger.error(f"Error disconnecting from WebView2: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def capture_screenshot(self, filename: Optional[str] = None) -> Dict[str, Any]:
        """
        捕获WebView2页面截图
        
        Args:
            filename: 截图文件名（可选）
            
        Returns:
            截图结果字典
        """
        try:
            if not self.ws:
                return {
                    'success': False,
                    'error': 'Not connected to WebView2'
                }
            
            if not filename:
                timestamp = int(time.time())
                filename = f"simple_webview2_screenshot_{timestamp}.png"
            
            # 确保文件路径是绝对路径
            if not Path(filename).is_absolute():
                filename = str(Path.cwd() / filename)
            
            # 发送截图命令
            response = self._send_command("Page.captureScreenshot", {
                "format": "png",
                "quality": 100
            })
            
            if 'result' in response and 'data' in response['result']:
                import base64
                screenshot_data = base64.b64decode(response['result']['data'])
                
                with open(filename, 'wb') as f:
                    f.write(screenshot_data)
                
                if self.visual_mode:
                    self.logger.info(f"📸 Screenshot saved: {filename}")
                    time.sleep(self.operation_delay)
                
                return {
                    'success': True,
                    'screenshot_path': filename
                }
            else:
                return {
                    'success': False,
                    'error': 'Failed to capture screenshot'
                }
            
        except Exception as e:
            self.logger.error(f"Failed to capture screenshot: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def execute_script(self, script: str) -> Dict[str, Any]:
        """
        在WebView2页面中执行JavaScript代码
        
        Args:
            script: JavaScript代码
            
        Returns:
            执行结果字典
        """
        try:
            if not self.ws:
                return {
                    'success': False,
                    'error': 'Not connected to WebView2'
                }
            
            response = self._send_command("Runtime.evaluate", {
                "expression": script,
                "returnByValue": True
            })
            
            if 'result' in response:
                result = response['result']
                if 'exceptionDetails' in result:
                    return {
                        'success': False,
                        'error': f"JavaScript error: {result['exceptionDetails']}"
                    }
                else:
                    value = result.get('result', {}).get('value')
                    
                    if self.visual_mode:
                        self.logger.info(f"🔧 Executed JavaScript: {script[:50]}...")
                        time.sleep(self.operation_delay)
                    
                    return {
                        'success': True,
                        'result': value
                    }
            else:
                return {
                    'success': False,
                    'error': 'Invalid response from WebView2'
                }
            
        except Exception as e:
            self.logger.error(f"Failed to execute script: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_page_source(self) -> Dict[str, Any]:
        """
        获取WebView2页面的HTML源码
        
        Returns:
            页面源码结果字典
        """
        try:
            result = self.execute_script("document.documentElement.outerHTML")
            if result['success']:
                return {
                    'success': True,
                    'source': result['result']
                }
            else:
                return result
            
        except Exception as e:
            self.logger.error(f"Failed to get page source: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取当前连接状态
        
        Returns:
            状态信息字典
        """
        try:
            if not self.ws:
                return {
                    'connected': False,
                    'debug_port': None,
                    'page_id': None
                }
            
            # 尝试获取当前页面信息
            url_result = self.execute_script("location.href")
            title_result = self.execute_script("document.title")
            
            return {
                'connected': True,
                'debug_port': self.debug_port,
                'page_id': self.page_id,
                'url': url_result.get('result') if url_result.get('success') else None,
                'title': title_result.get('result') if title_result.get('success') else None,
                'visual_mode': self.visual_mode
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get status: {e}")
            return {
                'connected': False,
                'error': str(e)
            }
