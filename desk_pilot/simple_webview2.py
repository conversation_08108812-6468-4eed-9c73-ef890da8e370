#!/usr/bin/env python3
"""
简化的WebView2自动化模块
直接使用Chrome DevTools Protocol，避免Playwright的高级功能限制
"""

import json
import logging
import time
import requests
import websocket
import threading
from typing import Dict, Any, Optional, List
import psutil
import re
from pathlib import Path

class SimpleWebView2Manager:
    """简化的WebView2自动化管理器，直接使用CDP"""
    
    def __init__(self, visual_mode: bool = True, operation_delay: float = 0.5):
        """
        初始化简化WebView2管理器
        
        Args:
            visual_mode: 是否启用可视化模式
            operation_delay: 操作间延迟时间（秒）
        """
        self.visual_mode = visual_mode
        self.operation_delay = operation_delay
        self.logger = logging.getLogger(__name__)
        
        # CDP连接相关
        self.debug_port = None
        self.ws = None
        self.page_id = None
        self.session_id = None
        self.message_id = 0
        
        self.logger.info(f"Simple WebView2 Manager initialized with visual mode: {visual_mode}")
    
    def detect_webview2_apps(self) -> List[Dict[str, Any]]:
        """
        检测系统中运行的WebView2应用
        
        Returns:
            包含WebView2应用信息的列表
        """
        webview2_apps = []
        
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    proc_info = proc.info
                    if not proc_info['cmdline']:
                        continue
                    
                    cmdline = ' '.join(proc_info['cmdline'])
                    
                    # 检测WebView2相关进程
                    is_webview2 = (
                        'webview2' in proc_info['name'].lower() or
                        'msedgewebview2' in proc_info['name'].lower() or
                        '--remote-debugging-port' in cmdline
                    )
                    
                    if is_webview2:
                        # 提取调试端口
                        debug_port = None
                        port_match = re.search(r'--remote-debugging-port[=\s](\d+)', cmdline)
                        if port_match:
                            debug_port = int(port_match.group(1))
                        
                        app_info = {
                            'pid': proc_info['pid'],
                            'name': proc_info['name'],
                            'debug_port': debug_port,
                            'cmdline': cmdline[:100] + '...' if len(cmdline) > 100 else cmdline,
                            'has_debug_port': debug_port is not None
                        }
                        webview2_apps.append(app_info)
                        
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
        
        except Exception as e:
            self.logger.error(f"Error detecting WebView2 apps: {e}")
        
        self.logger.info(f"Detected {len(webview2_apps)} potential WebView2 applications")
        return webview2_apps
    
    def connect_to_webview2(self, debug_port: int, timeout: int = 30) -> Dict[str, Any]:
        """
        连接到WebView2应用
        
        Args:
            debug_port: WebView2应用的调试端口
            timeout: 连接超时时间（秒）
            
        Returns:
            连接结果字典
        """
        try:
            if self.ws:
                self.logger.warning("Already connected to a WebView2 instance. Disconnecting first.")
                self.disconnect()
            
            self.debug_port = debug_port
            
            if self.visual_mode:
                self.logger.info(f"🔗 Connecting to WebView2 instance (port: {debug_port})")
            
            # 1. 获取可用的页面列表
            list_url = f"http://localhost:{debug_port}/json/list"
            response = requests.get(list_url, timeout=10)
            response.raise_for_status()
            
            pages = response.json()
            if not pages:
                raise Exception("No pages found in WebView2 application")
            
            # 选择第一个可用页面
            target_page = None
            for page in pages:
                if page.get('type') == 'page' and 'webSocketDebuggerUrl' in page:
                    target_page = page
                    break
            
            if not target_page:
                # 如果没有找到page类型，尝试使用第一个有websocket的
                for page in pages:
                    if 'webSocketDebuggerUrl' in page:
                        target_page = page
                        break
            
            if not target_page:
                raise Exception("No suitable page found for connection")
            
            self.page_id = target_page['id']
            ws_url = target_page['webSocketDebuggerUrl']
            
            if self.visual_mode:
                self.logger.info(f"📄 Found page: {target_page.get('title', 'Unknown')} - {target_page.get('url', 'Unknown')}")
            
            # 2. 建立WebSocket连接
            self.ws = websocket.create_connection(ws_url, timeout=timeout)
            
            # 3. 启用必要的域
            self._send_command("Runtime.enable")
            self._send_command("Page.enable")
            
            if self.visual_mode:
                self.logger.info("✅ Successfully connected to WebView2!")
                time.sleep(self.operation_delay)
            
            return {
                'success': True,
                'debug_port': debug_port,
                'page_id': self.page_id,
                'url': target_page.get('url'),
                'title': target_page.get('title')
            }
            
        except Exception as e:
            self.logger.error(f"Failed to connect to WebView2: {e}")
            self._cleanup_connection()
            return {
                'success': False,
                'error': str(e)
            }
    
    def _send_command(self, method: str, params: Optional[Dict] = None) -> Dict[str, Any]:
        """发送CDP命令"""
        if not self.ws:
            raise Exception("Not connected to WebView2")
        
        self.message_id += 1
        message = {
            "id": self.message_id,
            "method": method
        }
        if params:
            message["params"] = params
        
        self.ws.send(json.dumps(message))
        
        # 等待响应
        response = self.ws.recv()
        return json.loads(response)
    
    def _cleanup_connection(self):
        """清理连接"""
        if self.ws:
            try:
                self.ws.close()
            except:
                pass
            self.ws = None
        self.page_id = None
        self.session_id = None
        self.message_id = 0
    
    def disconnect(self) -> Dict[str, Any]:
        """
        断开WebView2连接
        
        Returns:
            断开连接结果字典
        """
        try:
            self._cleanup_connection()
            self.debug_port = None
            
            if self.visual_mode:
                self.logger.info("🔌 Disconnected from WebView2")
            
            return {'success': True}
            
        except Exception as e:
            self.logger.error(f"Error disconnecting from WebView2: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def capture_screenshot(self, filename: Optional[str] = None) -> Dict[str, Any]:
        """
        捕获WebView2页面截图
        
        Args:
            filename: 截图文件名（可选）
            
        Returns:
            截图结果字典
        """
        try:
            if not self.ws:
                return {
                    'success': False,
                    'error': 'Not connected to WebView2'
                }
            
            if not filename:
                timestamp = int(time.time())
                filename = f"simple_webview2_screenshot_{timestamp}.png"
            
            # 确保文件路径是绝对路径
            if not Path(filename).is_absolute():
                filename = str(Path.cwd() / filename)
            
            # 发送截图命令（使用更简单的参数）
            response = self._send_command("Page.captureScreenshot", {
                "format": "png"
            })
            
            if 'result' in response and 'data' in response['result']:
                import base64
                screenshot_data = base64.b64decode(response['result']['data'])
                
                with open(filename, 'wb') as f:
                    f.write(screenshot_data)
                
                if self.visual_mode:
                    self.logger.info(f"📸 Screenshot saved: {filename}")
                    time.sleep(self.operation_delay)
                
                return {
                    'success': True,
                    'screenshot_path': filename
                }
            else:
                return {
                    'success': False,
                    'error': 'Failed to capture screenshot'
                }
            
        except Exception as e:
            self.logger.error(f"Failed to capture screenshot: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def execute_script(self, script: str) -> Dict[str, Any]:
        """
        在WebView2页面中执行JavaScript代码
        
        Args:
            script: JavaScript代码
            
        Returns:
            执行结果字典
        """
        try:
            if not self.ws:
                return {
                    'success': False,
                    'error': 'Not connected to WebView2'
                }
            
            response = self._send_command("Runtime.evaluate", {
                "expression": script,
                "returnByValue": True
            })
            
            if 'result' in response:
                result = response['result']
                if 'exceptionDetails' in result:
                    return {
                        'success': False,
                        'error': f"JavaScript error: {result['exceptionDetails']}"
                    }
                else:
                    value = result.get('result', {}).get('value')
                    
                    if self.visual_mode:
                        self.logger.info(f"🔧 Executed JavaScript: {script[:50]}...")
                        time.sleep(self.operation_delay)
                    
                    return {
                        'success': True,
                        'result': value
                    }
            else:
                return {
                    'success': False,
                    'error': 'Invalid response from WebView2'
                }
            
        except Exception as e:
            self.logger.error(f"Failed to execute script: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_page_source(self) -> Dict[str, Any]:
        """
        获取WebView2页面的HTML源码
        
        Returns:
            页面源码结果字典
        """
        try:
            result = self.execute_script("document.documentElement.outerHTML")
            if result['success']:
                return {
                    'success': True,
                    'source': result['result']
                }
            else:
                return result
            
        except Exception as e:
            self.logger.error(f"Failed to get page source: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取当前连接状态

        Returns:
            状态信息字典
        """
        try:
            if not self.ws:
                return {
                    'connected': False,
                    'debug_port': None,
                    'page_id': None
                }

            # 尝试获取当前页面信息
            url_result = self.execute_script("location.href")
            title_result = self.execute_script("document.title")

            return {
                'connected': True,
                'debug_port': self.debug_port,
                'page_id': self.page_id,
                'url': url_result.get('result') if url_result.get('success') else None,
                'title': title_result.get('result') if title_result.get('success') else None,
                'visual_mode': self.visual_mode
            }

        except Exception as e:
            self.logger.error(f"Failed to get status: {e}")
            return {
                'connected': False,
                'error': str(e)
            }

    # ==================== 元素定位和等待功能 ====================

    def find_element(self, selector: str, selector_type: str = "css") -> Dict[str, Any]:
        """
        查找页面元素

        Args:
            selector: 选择器字符串
            selector_type: 选择器类型 ("css", "xpath", "id", "class", "tag")

        Returns:
            元素查找结果字典
        """
        try:
            if not self.ws:
                return {
                    'success': False,
                    'error': 'Not connected to WebView2'
                }

            # 根据选择器类型构建JavaScript查询
            if selector_type == "css":
                js_code = f"document.querySelector('{selector}')"
            elif selector_type == "xpath":
                js_code = f"""
                document.evaluate('{selector}', document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue
                """
            elif selector_type == "id":
                js_code = f"document.getElementById('{selector}')"
            elif selector_type == "class":
                js_code = f"document.getElementsByClassName('{selector}')[0]"
            elif selector_type == "tag":
                js_code = f"document.getElementsByTagName('{selector}')[0]"
            else:
                return {
                    'success': False,
                    'error': f'Unsupported selector type: {selector_type}'
                }

            # 执行查找并获取元素信息
            find_script = f"""
            (function() {{
                var element = {js_code};
                if (!element) return null;

                var rect = element.getBoundingClientRect();
                return {{
                    tagName: element.tagName,
                    id: element.id,
                    className: element.className,
                    textContent: element.textContent ? element.textContent.substring(0, 100) : '',
                    visible: rect.width > 0 && rect.height > 0 && element.offsetParent !== null,
                    x: rect.left + window.scrollX,
                    y: rect.top + window.scrollY,
                    width: rect.width,
                    height: rect.height,
                    centerX: rect.left + rect.width / 2 + window.scrollX,
                    centerY: rect.top + rect.height / 2 + window.scrollY
                }};
            }})()
            """

            result = self.execute_script(find_script)

            if result['success']:
                if result['result'] is None:
                    return {
                        'success': False,
                        'error': f'Element not found: {selector}'
                    }
                else:
                    if self.visual_mode:
                        self.logger.info(f"🎯 Found element: {selector} ({selector_type})")
                        time.sleep(self.operation_delay * 0.5)

                    return {
                        'success': True,
                        'element': result['result'],
                        'selector': selector,
                        'selector_type': selector_type
                    }
            else:
                return {
                    'success': False,
                    'error': f'Failed to execute find script: {result.get("error", "Unknown error")}'
                }

        except Exception as e:
            self.logger.error(f"Failed to find element: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def find_elements(self, selector: str, selector_type: str = "css") -> Dict[str, Any]:
        """
        查找多个页面元素

        Args:
            selector: 选择器字符串
            selector_type: 选择器类型 ("css", "xpath", "class", "tag")

        Returns:
            元素查找结果字典
        """
        try:
            if not self.ws:
                return {
                    'success': False,
                    'error': 'Not connected to WebView2'
                }

            # 根据选择器类型构建JavaScript查询
            if selector_type == "css":
                js_code = f"document.querySelectorAll('{selector}')"
            elif selector_type == "xpath":
                js_code = f"""
                (function() {{
                    var result = document.evaluate('{selector}', document, null, XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, null);
                    var elements = [];
                    for (var i = 0; i < result.snapshotLength; i++) {{
                        elements.push(result.snapshotItem(i));
                    }}
                    return elements;
                }})()
                """
            elif selector_type == "class":
                js_code = f"document.getElementsByClassName('{selector}')"
            elif selector_type == "tag":
                js_code = f"document.getElementsByTagName('{selector}')"
            else:
                return {
                    'success': False,
                    'error': f'Unsupported selector type: {selector_type}'
                }

            # 执行查找并获取元素信息
            find_script = f"""
            (function() {{
                var elements = {js_code};
                if (!elements || elements.length === 0) return [];

                var results = [];
                for (var i = 0; i < Math.min(elements.length, 50); i++) {{
                    var element = elements[i];
                    var rect = element.getBoundingClientRect();
                    results.push({{
                        index: i,
                        tagName: element.tagName,
                        id: element.id,
                        className: element.className,
                        textContent: element.textContent ? element.textContent.substring(0, 50) : '',
                        visible: rect.width > 0 && rect.height > 0 && element.offsetParent !== null,
                        x: rect.left + window.scrollX,
                        y: rect.top + window.scrollY,
                        width: rect.width,
                        height: rect.height,
                        centerX: rect.left + rect.width / 2 + window.scrollX,
                        centerY: rect.top + rect.height / 2 + window.scrollY
                    }});
                }}
                return results;
            }})()
            """

            result = self.execute_script(find_script)

            if result['success']:
                elements = result['result'] or []

                if self.visual_mode:
                    self.logger.info(f"🎯 Found {len(elements)} elements: {selector} ({selector_type})")
                    time.sleep(self.operation_delay * 0.5)

                return {
                    'success': True,
                    'elements': elements,
                    'count': len(elements),
                    'selector': selector,
                    'selector_type': selector_type
                }
            else:
                return {
                    'success': False,
                    'error': f'Failed to execute find script: {result.get("error", "Unknown error")}'
                }

        except Exception as e:
            self.logger.error(f"Failed to find elements: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def wait_for_element(self, selector: str, selector_type: str = "css",
                        timeout: int = 10, condition: str = "present") -> Dict[str, Any]:
        """
        等待元素出现或满足特定条件

        Args:
            selector: 选择器字符串
            selector_type: 选择器类型
            timeout: 超时时间（秒）
            condition: 等待条件 ("present", "visible", "clickable", "absent")

        Returns:
            等待结果字典
        """
        try:
            if not self.ws:
                return {
                    'success': False,
                    'error': 'Not connected to WebView2'
                }

            start_time = time.time()

            if self.visual_mode:
                self.logger.info(f"⏳ Waiting for element {condition}: {selector} (timeout: {timeout}s)")

            while time.time() - start_time < timeout:
                result = self.find_element(selector, selector_type)

                if condition == "absent":
                    # 等待元素消失
                    if not result['success']:
                        if self.visual_mode:
                            self.logger.info(f"✅ Element absent: {selector}")
                            time.sleep(self.operation_delay)
                        return {
                            'success': True,
                            'condition': condition,
                            'elapsed_time': time.time() - start_time
                        }
                elif result['success']:
                    element = result['element']

                    if condition == "present":
                        # 元素存在即可
                        if self.visual_mode:
                            self.logger.info(f"✅ Element present: {selector}")
                            time.sleep(self.operation_delay)
                        return {
                            'success': True,
                            'element': element,
                            'condition': condition,
                            'elapsed_time': time.time() - start_time
                        }
                    elif condition == "visible":
                        # 元素可见
                        if element.get('visible', False):
                            if self.visual_mode:
                                self.logger.info(f"✅ Element visible: {selector}")
                                time.sleep(self.operation_delay)
                            return {
                                'success': True,
                                'element': element,
                                'condition': condition,
                                'elapsed_time': time.time() - start_time
                            }
                    elif condition == "clickable":
                        # 元素可点击（可见且启用）
                        if element.get('visible', False):
                            # 检查元素是否启用
                            enabled_result = self.execute_script(f"""
                            (function() {{
                                var element = document.querySelector('{selector}');
                                return element && !element.disabled && element.style.pointerEvents !== 'none';
                            }})()
                            """)

                            if enabled_result.get('success') and enabled_result.get('result'):
                                if self.visual_mode:
                                    self.logger.info(f"✅ Element clickable: {selector}")
                                    time.sleep(self.operation_delay)
                                return {
                                    'success': True,
                                    'element': element,
                                    'condition': condition,
                                    'elapsed_time': time.time() - start_time
                                }

                # 短暂等待后重试
                time.sleep(0.5)

            # 超时
            if self.visual_mode:
                self.logger.warning(f"⏰ Timeout waiting for element {condition}: {selector}")

            return {
                'success': False,
                'error': f'Timeout waiting for element {condition}: {selector}',
                'timeout': timeout,
                'elapsed_time': time.time() - start_time
            }

        except Exception as e:
            self.logger.error(f"Failed to wait for element: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    # ==================== 交互功能 ====================

    def click_element(self, selector: str = None, selector_type: str = "css",
                     x: int = None, y: int = None, wait_timeout: int = 10) -> Dict[str, Any]:
        """
        点击元素或指定坐标

        Args:
            selector: 元素选择器（可选）
            selector_type: 选择器类型
            x: X坐标（可选）
            y: Y坐标（可选）
            wait_timeout: 等待元素超时时间

        Returns:
            点击结果字典
        """
        try:
            if not self.ws:
                return {
                    'success': False,
                    'error': 'Not connected to WebView2'
                }

            click_x, click_y = x, y

            # 如果提供了选择器，先查找元素
            if selector:
                # 等待元素可点击
                wait_result = self.wait_for_element(selector, selector_type, wait_timeout, "clickable")
                if not wait_result['success']:
                    return {
                        'success': False,
                        'error': f'Element not clickable: {wait_result.get("error", "Unknown error")}'
                    }

                element = wait_result['element']
                click_x = element['centerX']
                click_y = element['centerY']

                if self.visual_mode:
                    self.logger.info(f"🖱️ Clicking element: {selector} at ({click_x:.1f}, {click_y:.1f})")
            else:
                if click_x is None or click_y is None:
                    return {
                        'success': False,
                        'error': 'Either selector or coordinates (x, y) must be provided'
                    }

                if self.visual_mode:
                    self.logger.info(f"🖱️ Clicking coordinates: ({click_x}, {click_y})")

            # 发送鼠标点击事件
            # 首先发送鼠标按下事件
            mouse_down = self._send_command("Input.dispatchMouseEvent", {
                "type": "mousePressed",
                "x": click_x,
                "y": click_y,
                "button": "left",
                "clickCount": 1
            })

            # 短暂延迟
            time.sleep(0.05)

            # 发送鼠标释放事件
            mouse_up = self._send_command("Input.dispatchMouseEvent", {
                "type": "mouseReleased",
                "x": click_x,
                "y": click_y,
                "button": "left",
                "clickCount": 1
            })

            if self.visual_mode:
                self.logger.info("✅ Click completed")
                time.sleep(self.operation_delay)

            return {
                'success': True,
                'x': click_x,
                'y': click_y,
                'selector': selector,
                'method': 'element' if selector else 'coordinates'
            }

        except Exception as e:
            self.logger.error(f"Failed to click: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def type_text(self, text: str, selector: str = None, selector_type: str = "css",
                 clear_first: bool = True, wait_timeout: int = 10) -> Dict[str, Any]:
        """
        在元素中输入文本

        Args:
            text: 要输入的文本
            selector: 目标元素选择器（可选，如果不提供则在当前焦点元素输入）
            selector_type: 选择器类型
            clear_first: 是否先清空现有内容
            wait_timeout: 等待元素超时时间

        Returns:
            输入结果字典
        """
        try:
            if not self.ws:
                return {
                    'success': False,
                    'error': 'Not connected to WebView2'
                }

            # 如果提供了选择器，先点击元素获得焦点
            if selector:
                click_result = self.click_element(selector, selector_type, wait_timeout=wait_timeout)
                if not click_result['success']:
                    return {
                        'success': False,
                        'error': f'Failed to click input element: {click_result.get("error", "Unknown error")}'
                    }

                # 短暂等待焦点切换
                time.sleep(0.1)

                if self.visual_mode:
                    self.logger.info(f"⌨️ Typing text in element: {selector}")
            else:
                if self.visual_mode:
                    self.logger.info("⌨️ Typing text in focused element")

            # 如果需要清空现有内容
            if clear_first:
                # 全选现有内容
                self._send_command("Input.dispatchKeyEvent", {
                    "type": "keyDown",
                    "key": "Control"
                })
                self._send_command("Input.dispatchKeyEvent", {
                    "type": "char",
                    "text": "a"
                })
                self._send_command("Input.dispatchKeyEvent", {
                    "type": "keyUp",
                    "key": "Control"
                })

                time.sleep(0.05)

            # 逐字符输入文本
            for char in text:
                if char == '\n':
                    # 处理换行
                    self._send_command("Input.dispatchKeyEvent", {
                        "type": "keyDown",
                        "key": "Enter"
                    })
                    self._send_command("Input.dispatchKeyEvent", {
                        "type": "keyUp",
                        "key": "Enter"
                    })
                elif char == '\t':
                    # 处理制表符
                    self._send_command("Input.dispatchKeyEvent", {
                        "type": "keyDown",
                        "key": "Tab"
                    })
                    self._send_command("Input.dispatchKeyEvent", {
                        "type": "keyUp",
                        "key": "Tab"
                    })
                else:
                    # 普通字符
                    self._send_command("Input.dispatchKeyEvent", {
                        "type": "char",
                        "text": char
                    })

                # 短暂延迟模拟真实输入
                time.sleep(0.02)

            if self.visual_mode:
                self.logger.info(f"✅ Text input completed: '{text[:50]}{'...' if len(text) > 50 else ''}'")
                time.sleep(self.operation_delay)

            return {
                'success': True,
                'text': text,
                'selector': selector,
                'clear_first': clear_first
            }

        except Exception as e:
            self.logger.error(f"Failed to type text: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def scroll_to(self, x: int = None, y: int = None, selector: str = None,
                 selector_type: str = "css", behavior: str = "smooth") -> Dict[str, Any]:
        """
        滚动到指定位置或元素

        Args:
            x: X坐标（可选）
            y: Y坐标（可选）
            selector: 目标元素选择器（可选）
            selector_type: 选择器类型
            behavior: 滚动行为 ("smooth", "instant", "auto")

        Returns:
            滚动结果字典
        """
        try:
            if not self.ws:
                return {
                    'success': False,
                    'error': 'Not connected to WebView2'
                }

            if selector:
                # 滚动到元素
                find_result = self.find_element(selector, selector_type)
                if not find_result['success']:
                    return {
                        'success': False,
                        'error': f'Element not found for scrolling: {find_result.get("error", "Unknown error")}'
                    }

                element = find_result['element']
                scroll_script = f"""
                (function() {{
                    var element = document.querySelector('{selector}');
                    if (element) {{
                        element.scrollIntoView({{
                            behavior: '{behavior}',
                            block: 'center',
                            inline: 'center'
                        }});
                        return {{
                            x: window.pageXOffset,
                            y: window.pageYOffset,
                            elementX: {element['x']},
                            elementY: {element['y']}
                        }};
                    }}
                    return null;
                }})()
                """

                if self.visual_mode:
                    self.logger.info(f"📜 Scrolling to element: {selector}")

            elif x is not None or y is not None:
                # 滚动到坐标
                scroll_x = x if x is not None else "window.pageXOffset"
                scroll_y = y if y is not None else "window.pageYOffset"

                scroll_script = f"""
                (function() {{
                    window.scrollTo({{
                        left: {scroll_x},
                        top: {scroll_y},
                        behavior: '{behavior}'
                    }});
                    return {{
                        x: window.pageXOffset,
                        y: window.pageYOffset
                    }};
                }})()
                """

                if self.visual_mode:
                    self.logger.info(f"📜 Scrolling to coordinates: ({x}, {y})")
            else:
                return {
                    'success': False,
                    'error': 'Either coordinates (x, y) or selector must be provided'
                }

            result = self.execute_script(scroll_script)

            if result['success']:
                if self.visual_mode:
                    self.logger.info("✅ Scroll completed")
                    time.sleep(self.operation_delay)

                return {
                    'success': True,
                    'scroll_position': result['result'],
                    'target_x': x,
                    'target_y': y,
                    'selector': selector,
                    'behavior': behavior
                }
            else:
                return {
                    'success': False,
                    'error': f'Failed to execute scroll script: {result.get("error", "Unknown error")}'
                }

        except Exception as e:
            self.logger.error(f"Failed to scroll: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def scroll_by(self, delta_x: int = 0, delta_y: int = 0, behavior: str = "smooth") -> Dict[str, Any]:
        """
        相对滚动

        Args:
            delta_x: X方向滚动距离
            delta_y: Y方向滚动距离
            behavior: 滚动行为

        Returns:
            滚动结果字典
        """
        try:
            if not self.ws:
                return {
                    'success': False,
                    'error': 'Not connected to WebView2'
                }

            scroll_script = f"""
            (function() {{
                window.scrollBy({{
                    left: {delta_x},
                    top: {delta_y},
                    behavior: '{behavior}'
                }});
                return {{
                    x: window.pageXOffset,
                    y: window.pageYOffset,
                    deltaX: {delta_x},
                    deltaY: {delta_y}
                }};
            }})()
            """

            result = self.execute_script(scroll_script)

            if result['success']:
                if self.visual_mode:
                    self.logger.info(f"📜 Scrolled by: ({delta_x}, {delta_y})")
                    time.sleep(self.operation_delay)

                return {
                    'success': True,
                    'scroll_result': result['result'],
                    'delta_x': delta_x,
                    'delta_y': delta_y,
                    'behavior': behavior
                }
            else:
                return {
                    'success': False,
                    'error': f'Failed to execute scroll script: {result.get("error", "Unknown error")}'
                }

        except Exception as e:
            self.logger.error(f"Failed to scroll by: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    # ==================== 增强截图功能 ====================

    def capture_element_screenshot(self, selector: str, selector_type: str = "css",
                                  filename: Optional[str] = None, padding: int = 10) -> Dict[str, Any]:
        """
        捕获特定元素的截图

        Args:
            selector: 元素选择器
            selector_type: 选择器类型
            filename: 截图文件名（可选）
            padding: 截图边距

        Returns:
            截图结果字典
        """
        try:
            if not self.ws:
                return {
                    'success': False,
                    'error': 'Not connected to WebView2'
                }

            # 查找元素
            find_result = self.find_element(selector, selector_type)
            if not find_result['success']:
                return {
                    'success': False,
                    'error': f'Element not found for screenshot: {find_result.get("error", "Unknown error")}'
                }

            element = find_result['element']

            # 确保元素可见
            if not element.get('visible', False):
                return {
                    'success': False,
                    'error': 'Element is not visible for screenshot'
                }

            # 滚动到元素
            scroll_result = self.scroll_to(selector=selector, selector_type=selector_type)
            if not scroll_result['success']:
                self.logger.warning(f"Failed to scroll to element: {scroll_result.get('error')}")

            # 等待滚动完成
            time.sleep(0.5)

            # 重新获取元素位置（滚动后可能改变）
            find_result = self.find_element(selector, selector_type)
            if not find_result['success']:
                return {
                    'success': False,
                    'error': 'Element not found after scrolling'
                }

            element = find_result['element']

            # 计算截图区域
            clip_x = max(0, element['x'] - padding)
            clip_y = max(0, element['y'] - padding)
            clip_width = element['width'] + 2 * padding
            clip_height = element['height'] + 2 * padding

            if not filename:
                timestamp = int(time.time())
                filename = f"element_screenshot_{timestamp}.png"

            # 确保文件路径是绝对路径
            if not Path(filename).is_absolute():
                filename = str(Path.cwd() / filename)

            # 发送截图命令
            response = self._send_command("Page.captureScreenshot", {
                "format": "png",
                "clip": {
                    "x": clip_x,
                    "y": clip_y,
                    "width": clip_width,
                    "height": clip_height,
                    "scale": 1
                }
            })

            if 'result' in response and 'data' in response['result']:
                import base64
                screenshot_data = base64.b64decode(response['result']['data'])

                with open(filename, 'wb') as f:
                    f.write(screenshot_data)

                if self.visual_mode:
                    self.logger.info(f"📸 Element screenshot saved: {filename}")
                    time.sleep(self.operation_delay)

                return {
                    'success': True,
                    'screenshot_path': filename,
                    'element': element,
                    'clip_area': {
                        'x': clip_x,
                        'y': clip_y,
                        'width': clip_width,
                        'height': clip_height
                    }
                }
            else:
                return {
                    'success': False,
                    'error': 'Failed to capture element screenshot'
                }

        except Exception as e:
            self.logger.error(f"Failed to capture element screenshot: {e}")
            return {
                'success': False,
                'error': str(e)
            }
