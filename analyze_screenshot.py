#!/usr/bin/env python3
"""分析QuantumSeek窗口截图内容"""

from desk_pilot.core import DeskPilotCore
import base64
from PIL import Image
import io

def analyze_quantum_screenshot():
    print('🔍 重新测试并分析QuantumSeek窗口截图...')

    # 创建核心实例
    core = DeskPilotCore(visual_mode=True)

    try:
        # 测试QuantumSeek窗口截图
        result = core.capture_window('QuantumSeek')
        
        if isinstance(result, dict) and result.get('success'):
            base64_data = result['screenshot_base64']
            
            # 解码并保存截图
            image_data = base64.b64decode(base64_data)
            with open('quantum_analysis_screenshot.png', 'wb') as f:
                f.write(image_data)
            
            # 分析图片内容
            img = Image.open(io.BytesIO(image_data))
            print(f'📏 截图尺寸: {img.size}')
            
            # 窗口信息
            window_info = result['window_info']
            print(f'🪟 QuantumSeek窗口信息:')
            print(f'   位置: ({window_info["left"]}, {window_info["top"]})')
            print(f'   尺寸: {window_info["width"]} x {window_info["height"]}')
            
            # 分析截图内容 - 检查是否真的包含QuantumSeek界面
            print('\n🔍 分析截图内容...')
            
            # 由于我们使用的是全屏截图，需要检查QuantumSeek窗口区域
            window_left = window_info['left']
            window_top = window_info['top'] 
            window_width = window_info['width']
            window_height = window_info['height']
            
            # 检查窗口位置
            if window_left >= 3840:  # 副显示器
                print('⚠️ 窗口在副显示器上')
                print('🔧 全屏截图可能只包含主显示器内容，无法捕获副显示器窗口')
                print('❌ 这解释了为什么截图中看不到QuantumSeek界面')
            else:
                print('✅ 窗口在主显示器上，应该能正确截图')
                
            # 检查截图的主要颜色分布
            colors = img.getcolors(maxcolors=256*256*256)
            if colors:
                # 获取前5个最常见的颜色
                top_colors = sorted(colors, key=lambda x: x[0], reverse=True)[:5]
                print('\n🎨 截图中最常见的颜色:')
                for i, (count, color) in enumerate(top_colors):
                    percentage = (count / (img.size[0] * img.size[1])) * 100
                    print(f'   {i+1}. {color} - {percentage:.1f}%')
                    
                # 检查是否有紫色/粉色（QuantumSeek的主题色）
                has_purple = False
                for count, (r, g, b) in top_colors:
                    # 检查紫色/粉色范围
                    if (r > 100 and b > 100 and r > g) or (r > 150 and g < 100 and b > 100):
                        has_purple = True
                        break
                
                if has_purple:
                    print('✅ 检测到紫色/粉色，可能包含QuantumSeek界面')
                else:
                    print('❌ 未检测到紫色/粉色，可能未包含QuantumSeek界面')
            
            print('\n💾 截图已保存为: quantum_analysis_screenshot.png')
            print('🔍 请手动检查此文件是否包含QuantumSeek的紫色界面')
            
            # 提供修复建议
            if window_left >= 3840:
                print('\n🔧 问题诊断:')
                print('   当前的全屏截图方法只能捕获主显示器内容')
                print('   需要实现真正的多显示器截图支持')
                print('   建议使用Windows API或其他方法来捕获副显示器内容')
            
        else:
            print('❌ 截图失败')
            
    except Exception as e:
        print(f'❌ 测试失败: {str(e)}')
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    analyze_quantum_screenshot()
