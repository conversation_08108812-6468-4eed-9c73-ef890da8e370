#!/usr/bin/env python3
"""
调试WebView2元素查找问题
"""

import logging
from desk_pilot.simple_webview2 import SimpleWebView2Manager

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def debug_element_finding():
    """调试元素查找功能"""
    try:
        manager = SimpleWebView2Manager(visual_mode=True, operation_delay=0.5)
        
        print("🔍 调试WebView2元素查找功能")
        print("=" * 50)
        
        # 1. 检测应用
        apps = manager.detect_webview2_apps()
        debug_apps = [app for app in apps if app.get('has_debug_port')]
        
        if not debug_apps:
            print("❌ 没有找到有调试端口的WebView2应用")
            return
        
        # 2. 连接
        test_app = debug_apps[0]
        debug_port = test_app['debug_port']
        print(f"🔗 连接到端口: {debug_port}")
        
        connect_result = manager.connect_to_webview2(debug_port)
        if not connect_result['success']:
            print(f"❌ 连接失败: {connect_result['error']}")
            return
        
        print("✅ 连接成功!")
        
        # 3. 测试基础JavaScript执行
        print("\n🧪 测试基础JavaScript执行")
        print("-" * 30)
        
        # 简单测试
        simple_result = manager.execute_script("document.title")
        print(f"document.title 结果: {simple_result}")
        
        # 测试返回对象
        object_result = manager.execute_script("""
        (function() {
            return {
                test: "hello",
                number: 42,
                boolean: true
            };
        })()
        """)
        print(f"对象返回结果: {object_result}")
        
        # 4. 测试元素查找的JavaScript
        print("\n🎯 测试元素查找JavaScript")
        print("-" * 30)
        
        element_script = """
        (function() {
            var element = document.getElementsByTagName('body')[0];
            if (!element) return null;
            
            var rect = element.getBoundingClientRect();
            return {
                tagName: element.tagName,
                id: element.id,
                className: element.className,
                textContent: element.textContent ? element.textContent.substring(0, 50) : '',
                visible: rect.width > 0 && rect.height > 0 && element.offsetParent !== null,
                x: rect.left + window.scrollX,
                y: rect.top + window.scrollY,
                width: rect.width,
                height: rect.height
            };
        })()
        """
        
        element_result = manager.execute_script(element_script)
        print(f"元素查找JavaScript结果: {element_result}")
        
        # 5. 测试find_element方法
        print("\n🔍 测试find_element方法")
        print("-" * 30)

        # 先测试一个简单的脚本确保连接正常
        simple_test = manager.execute_script("'test_string'")
        print(f"简单字符串测试: {simple_test}")

        # 再测试一个对象
        object_test = manager.execute_script("({test: 'value', number: 123})")
        print(f"对象测试: {object_test}")

        # 现在测试find_element
        find_result = manager.find_element("body", "tag")
        print(f"find_element结果: {find_result}")

        if find_result['success']:
            element = find_result['element']
            print(f"元素类型: {type(element)}")
            print(f"元素内容: {element}")

            if isinstance(element, dict):
                print("✅ 元素是字典，包含以下键:")
                for key, value in element.items():
                    print(f"  {key}: {value}")
            else:
                print(f"❌ 元素不是字典，而是: {type(element)}")

        # 6. 再次测试find_element确保一致性
        print("\n🔄 再次测试find_element")
        print("-" * 30)

        find_result2 = manager.find_element("body", "tag")
        print(f"第二次find_element结果: {find_result2}")

        # 7. 断开连接
        manager.disconnect()
        print("\n✅ 调试完成")
        
    except Exception as e:
        print(f"❌ 调试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_element_finding()
