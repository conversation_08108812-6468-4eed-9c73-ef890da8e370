#!/usr/bin/env python3
"""对比之前成功的quantum_current_win32.png和现在的截图"""

from PIL import Image
import os

def analyze_screenshot_detailed(filename, label):
    """详细分析截图"""
    print(f'\n🔍 分析{label}: {filename}')
    
    if not os.path.exists(filename):
        print(f'   ❌ 文件不存在')
        return None
    
    try:
        with Image.open(filename) as img:
            width, height = img.size
            pixels = list(img.getdata())
            total_pixels = len(pixels)
            
            # 详细统计
            stats = {
                'width': width,
                'height': height,
                'total_pixels': total_pixels,
                'non_black_pixels': 0,
                'purple_pixels': 0,
                'bright_pixels': 0,
                'r_sum': 0,
                'g_sum': 0,
                'b_sum': 0,
                'brightness_distribution': {
                    'very_dark': 0,    # 0-30
                    'dark': 0,         # 31-80
                    'medium': 0,       # 81-150
                    'bright': 0,       # 151-255
                }
            }
            
            for pixel in pixels:
                r, g, b = pixel[:3]
                stats['r_sum'] += r
                stats['g_sum'] += g
                stats['b_sum'] += b
                
                brightness = (r + g + b) / 3
                
                # 亮度分类
                if brightness <= 30:
                    stats['brightness_distribution']['very_dark'] += 1
                elif brightness <= 80:
                    stats['brightness_distribution']['dark'] += 1
                elif brightness <= 150:
                    stats['brightness_distribution']['medium'] += 1
                else:
                    stats['brightness_distribution']['bright'] += 1
                
                # 非黑色像素
                if r > 15 or g > 15 or b > 15:
                    stats['non_black_pixels'] += 1
                
                # 紫色像素
                if b > 80 and b > r and b > g:
                    stats['purple_pixels'] += 1
                
                # 明亮像素
                if brightness > 100:
                    stats['bright_pixels'] += 1
            
            # 计算比例和平均值
            stats['avg_r'] = stats['r_sum'] / total_pixels
            stats['avg_g'] = stats['g_sum'] / total_pixels
            stats['avg_b'] = stats['b_sum'] / total_pixels
            stats['avg_brightness'] = (stats['avg_r'] + stats['avg_g'] + stats['avg_b']) / 3
            stats['non_black_ratio'] = stats['non_black_pixels'] / total_pixels
            stats['purple_ratio'] = stats['purple_pixels'] / total_pixels
            stats['bright_ratio'] = stats['bright_pixels'] / total_pixels
            
            # 打印结果
            print(f'   📏 尺寸: {width} x {height}')
            print(f'   🎨 平均颜色: R={stats["avg_r"]:.1f}, G={stats["avg_g"]:.1f}, B={stats["avg_b"]:.1f}')
            print(f'   💡 平均亮度: {stats["avg_brightness"]:.1f}')
            print(f'   📊 非黑色像素: {stats["non_black_ratio"]:.2%}')
            print(f'   🟣 紫色像素: {stats["purple_ratio"]:.2%}')
            print(f'   ✨ 明亮像素: {stats["bright_ratio"]:.2%}')
            
            print(f'   🌈 亮度分布:')
            for level, count in stats['brightness_distribution'].items():
                ratio = count / total_pixels
                print(f'     {level}: {ratio:.1%}')
            
            # 区域分析
            print(f'   📍 区域分析:')
            analyze_regions(img)
            
            return stats
            
    except Exception as e:
        print(f'   ❌ 分析失败: {e}')
        return None

def analyze_regions(img):
    """分析图像区域"""
    width, height = img.size
    
    regions = {
        '左上': (0, 0, width//3, height//3),
        '中上': (width//3, 0, 2*width//3, height//3),
        '右上': (2*width//3, 0, width, height//3),
        '左中': (0, height//3, width//3, 2*height//3),
        '中央': (width//3, height//3, 2*width//3, 2*height//3),
        '右中': (2*width//3, height//3, width, 2*height//3),
        '左下': (0, 2*height//3, width//3, height),
        '中下': (width//3, 2*height//3, 2*width//3, height),
        '右下': (2*width//3, 2*height//3, width, height),
    }
    
    for region_name, (x1, y1, x2, y2) in regions.items():
        try:
            region = img.crop((x1, y1, x2, y2))
            pixels = list(region.getdata())
            
            if not pixels:
                continue
            
            non_black = sum(1 for p in pixels if sum(p[:3]) > 45)
            ratio = non_black / len(pixels)
            
            if ratio > 0.5:
                status = '✅'
            elif ratio > 0.1:
                status = 'ℹ️'
            else:
                status = '❌'
            
            print(f'     {region_name}: {ratio:.1%} {status}')
            
        except Exception as e:
            print(f'     {region_name}: 分析失败')

def compare_screenshots():
    """对比截图"""
    print('🔬 QuantumSeek截图对比分析')
    print('=' * 60)
    
    # 分析之前成功的截图
    old_stats = analyze_screenshot_detailed('quantum_current_win32.png', '之前成功的截图')
    
    # 分析现在的截图
    new_stats = analyze_screenshot_detailed('final_quantum_screenshot.png', '当前截图')
    
    # 对比分析
    if old_stats and new_stats:
        print(f'\n📊 对比分析:')
        print(f'=' * 40)
        
        # 尺寸对比
        if old_stats['width'] == new_stats['width'] and old_stats['height'] == new_stats['height']:
            print(f'✅ 尺寸一致: {old_stats["width"]} x {old_stats["height"]}')
        else:
            print(f'⚠️ 尺寸不同: {old_stats["width"]}x{old_stats["height"]} vs {new_stats["width"]}x{new_stats["height"]}')
        
        # 内容质量对比
        print(f'\n📈 内容质量对比:')
        print(f'   非黑色像素: {old_stats["non_black_ratio"]:.2%} → {new_stats["non_black_ratio"]:.2%}')
        print(f'   紫色像素: {old_stats["purple_ratio"]:.2%} → {new_stats["purple_ratio"]:.2%}')
        print(f'   明亮像素: {old_stats["bright_ratio"]:.2%} → {new_stats["bright_ratio"]:.2%}')
        print(f'   平均亮度: {old_stats["avg_brightness"]:.1f} → {new_stats["avg_brightness"]:.1f}')
        
        # 质量评估
        print(f'\n🎯 质量评估:')
        
        if new_stats["non_black_ratio"] >= old_stats["non_black_ratio"] * 0.8:
            print(f'✅ 内容丰富度: 保持良好')
        else:
            print(f'❌ 内容丰富度: 显著下降')
        
        if new_stats["purple_ratio"] >= old_stats["purple_ratio"] * 0.5:
            print(f'✅ 紫色主题: 保持良好')
        else:
            print(f'❌ 紫色主题: 显著下降')
        
        if abs(new_stats["avg_brightness"] - old_stats["avg_brightness"]) < 5:
            print(f'✅ 亮度水平: 基本一致')
        else:
            print(f'⚠️ 亮度水平: 有差异')
    
    # 检查其他相关截图文件
    print(f'\n📁 其他截图文件检查:')
    screenshot_files = [
        'quantum_current_test.png',
        'method1_deskpilot.png',
        'quantum_screenshot.png',
        'quantum_test_screenshot.png'
    ]
    
    for filename in screenshot_files:
        if os.path.exists(filename):
            print(f'   ✅ {filename} 存在')
            try:
                with Image.open(filename) as img:
                    pixels = list(img.getdata())
                    non_black = sum(1 for p in pixels if sum(p[:3]) > 45)
                    ratio = non_black / len(pixels)
                    print(f'      内容比例: {ratio:.2%}')
            except:
                print(f'      ❌ 无法分析')
        else:
            print(f'   ❌ {filename} 不存在')

def main():
    """主函数"""
    try:
        compare_screenshots()
        
        print(f'\n💡 分析建议:')
        print(f'   1. 如果之前的截图质量更好，说明最近的修改引入了问题')
        print(f'   2. 检查是否修改了截图方法或参数')
        print(f'   3. 确认窗口状态和环境是否发生变化')
        print(f'   4. 考虑回退到之前成功的实现方式')
        
    except Exception as e:
        print(f'❌ 对比分析失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
