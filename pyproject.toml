[project]
name = "desk-pilot"
version = "0.1.0"
description = "A Model Context Protocol (MCP) tool for Windows desktop automation and Tauri development assistance"
authors = [
    {name = "DeskPilot Team"}
]
readme = "README.md"
requires-python = ">=3.12"
keywords = ["mcp", "desktop-automation", "tauri", "windows", "screenshot", "ui-automation"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: Microsoft :: Windows",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: System :: Systems Administration",
]

dependencies = [
    "mcp>=1.2.0",
    "pyautogui>=0.9.54",
    "pygetwindow>=0.0.9",
    "pillow>=10.0.0",
    "rich>=13.0.0",
    "selenium>=4.15.0",
    "webdriver-manager>=4.0.0",
    "psutil>=5.9.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.5.0",
]

[[tool.uv.index]]
url = "https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple"
default = true

[project.scripts]
desk-pilot = "desk_pilot.__main__:main"

[project.urls]
Homepage = "https://github.com/your-username/desk-pilot"
Repository = "https://github.com/your-username/desk-pilot"
Documentation = "https://github.com/your-username/desk-pilot/docs"
Issues = "https://github.com/your-username/desk-pilot/issues"
