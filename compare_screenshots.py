#!/usr/bin/env python3
"""比较不同截图文件"""

from PIL import Image
import os

def compare_screenshots():
    """比较截图文件"""
    files = [
        'quantum_current_win32.png',
        'deskpilot_correct_method.png',
        'final_quantum_screenshot.png'
    ]
    
    print('📸 截图文件对比:')
    print('=' * 50)
    
    for filename in files:
        if os.path.exists(filename):
            try:
                img = Image.open(filename)
                pixels = list(img.getdata())
                total_pixels = len(pixels)
                non_black_pixels = sum(1 for p in pixels if sum(p[:3]) > 45)
                non_black_ratio = non_black_pixels / total_pixels
                
                print(f'\n📁 {filename}:')
                print(f'   📏 尺寸: {img.size}')
                print(f'   📊 非黑色像素: {non_black_ratio:.2%}')
                
                if non_black_ratio > 0.4:
                    print(f'   ✅ 质量: 良好')
                elif non_black_ratio > 0.1:
                    print(f'   ⚠️ 质量: 一般')
                else:
                    print(f'   ❌ 质量: 失败(主要是黑色)')
                    
            except Exception as e:
                print(f'\n📁 {filename}: ❌ 读取失败 - {e}')
        else:
            print(f'\n📁 {filename}: ❌ 文件不存在')

if __name__ == '__main__':
    compare_screenshots()
