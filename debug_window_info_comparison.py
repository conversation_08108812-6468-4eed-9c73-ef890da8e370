#!/usr/bin/env python3
"""对比不同方法获取的QuantumSeek窗口信息"""

import pygetwindow as gw
import win32gui
import win32ui
import win32con
from PIL import Image
import time

def get_window_info_pygetwindow():
    """使用pygetwindow获取窗口信息"""
    print('\n🔍 方法1: pygetwindow获取窗口信息')
    
    windows = gw.getWindowsWithTitle('QuantumSeek')
    if not windows:
        print('❌ 未找到QuantumSeek窗口')
        return None
    
    window = windows[0]
    info = {
        'method': 'pygetwindow',
        'title': window.title,
        'left': window.left,
        'top': window.top,
        'width': window.width,
        'height': window.height,
        'right': window.left + window.width,
        'bottom': window.top + window.height,
        'isActive': window.isActive,
        'isMinimized': window.isMinimized,
        'isMaximized': window.isMaximized
    }
    
    print(f'   标题: {info["title"]}')
    print(f'   位置: ({info["left"]}, {info["top"]})')
    print(f'   尺寸: {info["width"]} x {info["height"]}')
    print(f'   右下: ({info["right"]}, {info["bottom"]})')
    print(f'   状态: 激活={info["isActive"]}, 最小化={info["isMinimized"]}, 最大化={info["isMaximized"]}')
    
    return info

def get_window_info_win32():
    """使用Win32 API获取窗口信息"""
    print('\n🔍 方法2: Win32 API获取窗口信息')
    
    hwnd = win32gui.FindWindow(None, 'QuantumSeek - 量子文件搜索')
    if not hwnd:
        print('❌ 未找到QuantumSeek窗口句柄')
        return None
    
    # 获取窗口矩形
    rect = win32gui.GetWindowRect(hwnd)
    client_rect = win32gui.GetClientRect(hwnd)
    
    info = {
        'method': 'win32api',
        'hwnd': hwnd,
        'window_rect': rect,
        'client_rect': client_rect,
        'left': rect[0],
        'top': rect[1],
        'right': rect[2],
        'bottom': rect[3],
        'width': rect[2] - rect[0],
        'height': rect[3] - rect[1],
        'client_width': client_rect[2] - client_rect[0],
        'client_height': client_rect[3] - client_rect[1],
        'isVisible': win32gui.IsWindowVisible(hwnd),
        'isIconic': win32gui.IsIconic(hwnd)
    }
    
    print(f'   窗口句柄: {info["hwnd"]}')
    print(f'   窗口矩形: {info["window_rect"]}')
    print(f'   客户区矩形: {info["client_rect"]}')
    print(f'   位置: ({info["left"]}, {info["top"]})')
    print(f'   尺寸: {info["width"]} x {info["height"]}')
    print(f'   客户区尺寸: {info["client_width"]} x {info["client_height"]}')
    print(f'   状态: 可见={info["isVisible"]}, 最小化={info["isIconic"]}')
    
    return info

def test_screenshot_with_info(info, method_name):
    """使用指定信息进行截图测试"""
    print(f'\n📸 使用{method_name}信息进行截图测试')
    
    try:
        # 使用与test_quantum_current.py相同的方法
        hdesktop = win32gui.GetDesktopWindow()
        desktop_dc = win32gui.GetWindowDC(hdesktop)
        img_dc = win32ui.CreateDCFromHandle(desktop_dc)
        mem_dc = img_dc.CreateCompatibleDC()
        
        # 创建位图
        screenshot = win32ui.CreateBitmap()
        screenshot.CreateCompatibleBitmap(img_dc, info['width'], info['height'])
        mem_dc.SelectObject(screenshot)
        
        # 复制窗口区域到位图
        mem_dc.BitBlt((0, 0), (info['width'], info['height']), img_dc, (info['left'], info['top']), win32con.SRCCOPY)
        
        # 获取位图数据
        bmpinfo = screenshot.GetInfo()
        bmpstr = screenshot.GetBitmapBits(True)
        
        # 转换为PIL Image
        img = Image.frombuffer(
            'RGB',
            (bmpinfo['bmWidth'], bmpinfo['bmHeight']),
            bmpstr, 'raw', 'BGRX', 0, 1
        )
        
        # 清理资源
        mem_dc.DeleteDC()
        img_dc.DeleteDC()
        win32gui.ReleaseDC(hdesktop, desktop_dc)
        win32gui.DeleteObject(screenshot.GetHandle())
        
        # 保存截图
        filename = f'debug_{method_name}_screenshot.png'
        img.save(filename)
        print(f'   ✅ 截图成功: {filename}')
        print(f'   📏 截图尺寸: {img.size}')
        
        # 分析内容
        pixels = list(img.getdata())
        total_pixels = len(pixels)
        non_black_pixels = sum(1 for p in pixels if sum(p[:3]) > 45)
        non_black_ratio = non_black_pixels / total_pixels
        
        print(f'   📊 非黑色像素: {non_black_ratio:.2%}')
        
        if non_black_ratio > 0.4:
            print(f'   ✅ 截图质量良好')
        elif non_black_ratio > 0.1:
            print(f'   ⚠️ 截图质量一般')
        else:
            print(f'   ❌ 截图主要是黑色')
        
        return img, non_black_ratio
        
    except Exception as e:
        print(f'   ❌ 截图失败: {e}')
        return None, 0

def compare_window_info():
    """对比不同方法获取的窗口信息"""
    print('🔬 QuantumSeek窗口信息对比分析')
    print('=' * 60)
    
    # 获取窗口信息
    pyget_info = get_window_info_pygetwindow()
    win32_info = get_window_info_win32()
    
    if not pyget_info or not win32_info:
        print('❌ 无法获取窗口信息')
        return
    
    # 对比信息
    print('\n📊 信息对比:')
    print('=' * 40)
    
    print(f'位置对比:')
    print(f'   pygetwindow: ({pyget_info["left"]}, {pyget_info["top"]})')
    print(f'   win32api:    ({win32_info["left"]}, {win32_info["top"]})')
    
    print(f'尺寸对比:')
    print(f'   pygetwindow: {pyget_info["width"]} x {pyget_info["height"]}')
    print(f'   win32api:    {win32_info["width"]} x {win32_info["height"]}')
    print(f'   win32客户区: {win32_info["client_width"]} x {win32_info["client_height"]}')
    
    # 检查差异
    pos_diff = (abs(pyget_info["left"] - win32_info["left"]), abs(pyget_info["top"] - win32_info["top"]))
    size_diff = (abs(pyget_info["width"] - win32_info["width"]), abs(pyget_info["height"] - win32_info["height"]))
    
    print(f'\n🔍 差异分析:')
    print(f'   位置差异: {pos_diff}')
    print(f'   尺寸差异: {size_diff}')
    
    if pos_diff[0] > 10 or pos_diff[1] > 10:
        print('   ⚠️ 位置差异较大')
    if size_diff[0] > 10 or size_diff[1] > 10:
        print('   ⚠️ 尺寸差异较大')
    
    # 测试截图
    print('\n📸 截图测试对比:')
    print('=' * 40)
    
    img1, ratio1 = test_screenshot_with_info(pyget_info, 'pygetwindow')
    img2, ratio2 = test_screenshot_with_info(win32_info, 'win32api')
    
    # 对比结果
    print(f'\n🎯 截图质量对比:')
    print(f'   pygetwindow方法: {ratio1:.2%}')
    print(f'   win32api方法:    {ratio2:.2%}')
    
    if ratio2 > ratio1:
        print('   ✅ Win32 API方法更好')
    elif ratio1 > ratio2:
        print('   ✅ pygetwindow方法更好')
    else:
        print('   ⚖️ 两种方法效果相同')
    
    # 建议
    print(f'\n💡 建议:')
    if ratio1 > 0.4:
        print('   ✅ pygetwindow方法可用，建议使用')
    elif ratio2 > 0.4:
        print('   ✅ Win32 API方法可用，建议切换到此方法')
    else:
        print('   ❌ 两种方法都有问题，需要进一步调试')

def main():
    """主函数"""
    try:
        compare_window_info()
        
    except Exception as e:
        print(f'❌ 对比分析失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
