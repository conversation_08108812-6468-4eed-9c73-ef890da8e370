#!/usr/bin/env python3
"""详细测试Windows API多显示器截图"""

import win32gui
import win32ui
import win32con
import win32api
from PIL import Image
import pygetwindow as gw

def test_win32_detailed():
    print('🔍 详细测试Windows API多显示器截图...')
    
    # 获取QuantumSeek窗口信息
    windows = gw.getWindowsWithTitle('QuantumSeek')
    if not windows:
        print('❌ 未找到QuantumSeek窗口')
        return
    
    window = windows[0]
    left, top, width, height = window.left, window.top, window.width, window.height
    print(f'📍 窗口位置: ({left}, {top})')
    print(f'📏 窗口尺寸: {width} x {height}')
    
    try:
        print('\n🔧 开始Windows API截图过程...')
        
        # 步骤1: 获取桌面窗口
        print('1. 获取桌面窗口...')
        hdesktop = win32gui.GetDesktopWindow()
        print(f'   桌面窗口句柄: {hdesktop}')
        
        # 步骤2: 获取桌面设备上下文
        print('2. 获取桌面设备上下文...')
        desktop_dc = win32gui.GetWindowDC(hdesktop)
        print(f'   桌面DC: {desktop_dc}')
        
        # 步骤3: 创建兼容的设备上下文
        print('3. 创建兼容的设备上下文...')
        img_dc = win32ui.CreateDCFromHandle(desktop_dc)
        print(f'   图像DC: {img_dc}')
        
        mem_dc = img_dc.CreateCompatibleDC()
        print(f'   内存DC: {mem_dc}')
        
        # 步骤4: 创建位图
        print('4. 创建位图...')
        win32_screenshot = win32ui.CreateBitmap()
        print(f'   位图对象: {win32_screenshot}')
        
        win32_screenshot.CreateCompatibleBitmap(img_dc, width, height)
        print(f'   创建兼容位图: {width}x{height}')
        
        # 步骤5: 选择位图到内存DC
        print('5. 选择位图到内存DC...')
        old_bitmap = mem_dc.SelectObject(win32_screenshot)
        print(f'   旧位图: {old_bitmap}')
        
        # 步骤6: 执行BitBlt操作
        print('6. 执行BitBlt操作...')
        print(f'   源坐标: ({left}, {top})')
        print(f'   目标尺寸: {width}x{height}')
        
        result = mem_dc.BitBlt((0, 0), (width, height), img_dc, (left, top), win32con.SRCCOPY)
        print(f'   BitBlt结果: {result}')
        
        if result == 0:
            error_code = win32api.GetLastError()
            print(f'   ❌ BitBlt失败，错误代码: {error_code}')
            return
        
        # 步骤7: 获取位图信息
        print('7. 获取位图信息...')
        bmpinfo = win32_screenshot.GetInfo()
        print(f'   位图信息: {bmpinfo}')
        
        # 步骤8: 获取位图数据
        print('8. 获取位图数据...')
        bmpstr = win32_screenshot.GetBitmapBits(True)
        print(f'   位图数据长度: {len(bmpstr)} 字节')
        
        # 步骤9: 转换为PIL Image
        print('9. 转换为PIL Image...')
        screenshot = Image.frombuffer(
            'RGB',
            (bmpinfo['bmWidth'], bmpinfo['bmHeight']),
            bmpstr, 'raw', 'BGRX', 0, 1
        )
        print(f'   PIL图像尺寸: {screenshot.size}')
        
        # 步骤10: 保存截图
        print('10. 保存截图...')
        screenshot.save('win32_detailed_test.png')
        print('    保存为: win32_detailed_test.png')
        
        # 步骤11: 分析截图内容
        print('11. 分析截图内容...')
        colors = screenshot.getcolors(maxcolors=256*256*256)
        if colors:
            top_colors = sorted(colors, key=lambda x: x[0], reverse=True)[:5]
            print('    前5个最常见的颜色:')
            for i, (count, color) in enumerate(top_colors):
                percentage = (count / (screenshot.size[0] * screenshot.size[1])) * 100
                print(f'      {i+1}. {color} - {percentage:.1f}%')
        
        # 步骤12: 清理资源
        print('12. 清理资源...')
        mem_dc.SelectObject(old_bitmap)
        mem_dc.DeleteDC()
        img_dc.DeleteDC()
        win32gui.ReleaseDC(hdesktop, desktop_dc)
        win32gui.DeleteObject(win32_screenshot.GetHandle())
        print('    资源清理完成')
        
        print('\n✅ Windows API截图成功完成！')
        
    except Exception as e:
        print(f'\n❌ Windows API截图失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_win32_detailed()
